<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shake & Match</title>
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    <link rel="stylesheet" href="/css/app.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <!-- Socket.IO -->
    <script src="/socket.io/socket.io.js"></script>
    <!-- SVG Icons -->
    <svg xmlns="http://www.w3.org/2000/svg" style="display:none">
        <symbol id="icon-heart" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
        </symbol>
        <symbol id="icon-settings" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .***********.97L2.46 14.6c-.19.15-.24.42-.12.64l2 3.46c.***********.61.22l2.49-1c.52.39 1.06.73 1.69.98l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.25 1.17-.59 1.69-.98l2.49 1c.22.09.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z"/>
        </symbol>
        <symbol id="icon-user" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
        </symbol>
        <symbol id="icon-chat" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 2H4c-1.1 0-2 .9-2 2v12c0 1.1.9 2 2 2h4l4 4 4-4h4c1.1 0 2-.9 2-2V4c0-1.1-.9-2-2-2z"/>
        </symbol>
        <symbol id="icon-send" viewBox="0 0 24 24" fill="currentColor">
            <path d="M2.01 21L23 12 2.01 3 2 10l15 2-15 2z"/>
        </symbol>
        <symbol id="icon-phone" viewBox="0 0 24 24" fill="currentColor">
            <path d="M17 1.01L7 1c-1.1 0-2 .9-2 2v18c0 1.1.9 2 2 2h10c1.1 0 2-.9 2-2V3c0-1.1-.9-1.99-2-1.99zM17 19H7V5h10v14z"/>
        </symbol>
        <symbol id="icon-location" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
        </symbol>
        <symbol id="icon-close" viewBox="0 0 24 24" fill="currentColor">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/>
        </symbol>
        <symbol id="icon-arrow-back" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </symbol>
        <symbol id="icon-google" viewBox="0 0 24 24" fill="currentColor">
            <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
        </symbol>
        <symbol id="icon-facebook" viewBox="0 0 24 24" fill="currentColor">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
        </symbol>
        <symbol id="icon-apple" viewBox="0 0 24 24" fill="currentColor">
            <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/>
        </symbol>
    </svg>
</head>
<body>
    <!-- App Container -->
    <div id="app" class="app-container">
        <!-- Login Screen -->
        <div id="login-screen" class="screen active">
            <div class="login-container">
                <div class="login-content">
                    <div class="logo-section">
                        <div class="logo-icon"></div>
                        <h1>Shake & Match</h1>
                        <p>Find your perfect match nearby</p>
                    </div>
                    
                    <div class="auth-section">
                        <div class="social-login">
                            <button class="social-btn google-btn" onclick="handleSocialLogin('google')">
                                <svg class="social-icon"><use href="#icon-google"/></svg>
                                Continue with Google
                            </button>
                            <button class="social-btn facebook-btn" onclick="handleSocialLogin('facebook')">
                                <svg class="social-icon"><use href="#icon-facebook"/></svg>
                                Continue with Facebook
                            </button>
                            <button class="social-btn apple-btn" onclick="handleSocialLogin('apple')">
                                <svg class="social-icon"><use href="#icon-apple"/></svg>
                                Continue with Apple
                            </button>
                        </div>
                        
                        <div class="divider">
                            <span>or</span>
                        </div>
                        
                        <form class="login-form" onsubmit="handleLogin(event)">
                            <div class="form-group">
                                <input type="text" id="username" placeholder="Username" required>
                            </div>
                            <div class="form-group">
                                <input type="password" id="password" placeholder="Password" required>
                            </div>
                            <button type="submit" class="login-btn">Sign In</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main App Screen -->
        <div id="main-screen" class="screen">
            <!-- Floating Decorative Shapes -->
            <div class="floating-shape floating-shape-1"></div>
            <div class="floating-shape floating-shape-2"></div>
            <div class="floating-shape floating-shape-3"></div>

            <!-- Navigation Header -->
            <header class="app-header">
                <div class="header-content">
                    <button class="nav-btn profile-btn" onclick="showScreen('profile')" id="profile-btn">
                        <div class="profile-icon-container">
                            <svg class="nav-icon default-profile-icon"><use href="#icon-user"/></svg>
                        </div>
                    </button>
                    <h1 class="app-title">Shake & Match</h1>
                    <button class="nav-btn settings-btn" onclick="showScreen('settings')">
                        <svg class="nav-icon"><use href="#icon-settings"/></svg>
                    </button>
                </div>
            </header>

            <!-- Main Content Area -->
            <main class="main-content">
                <!-- Home View -->
                <div id="home-view" class="view active">
                    <!-- Welcome Section -->
                    <div class="welcome-section">
                        <h2 class="welcome-text" id="welcome-text">Hi, User!</h2>
                        <p class="instruction-text">Shake your phone to match your soul! ♡</p>
                    </div>

                    <!-- Shake Section -->
                    <div class="shake-section">
                        <div class="shake-container">
                            <div class="shake-outer-circle">
                                <button class="shake-button" onclick="handleShake()" id="shake-btn">
                                    <svg class="shake-icon"><use href="#icon-phone"/></svg>
                                    <span class="shake-text">SHAKE</span>
                                </button>
                            </div>
                            <div class="bubble-container">
                                <div class="searching-bubble" id="searching-bubble" style="display: none;">
                                    <p class="searching-text">Searching for nearby matches...</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Matches Section -->
                    <div class="matches-section">
                        <h2 class="section-title">Your Matches</h2>
                        <div id="matches-list" class="matches-list">
                            <div class="no-matches">
                                <svg class="no-matches-icon"><use href="#icon-heart"/></svg>
                                <p>No matches yet.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat View -->
                <div id="chat-view" class="view">
                    <div class="chat-header">
                        <button class="back-btn" onclick="showView('home')">
                            <svg><use href="#icon-arrow-back"/></svg>
                        </button>
                        <div class="chat-user-info">
                            <div class="chat-avatar" onclick="viewProfile()"></div>
                            <span class="chat-username"></span>
                        </div>
                    </div>
                    <div class="chat-messages" id="chat-messages"></div>
                    <div class="chat-input-container">
                        <input type="text" class="chat-input" placeholder="Type a message..." onkeypress="handleChatKeyPress(event)">
                        <button class="send-btn" onclick="sendMessage()">
                            <svg><use href="#icon-send"/></svg>
                        </button>
                    </div>
                </div>

                <!-- Profile View -->
                <div id="profile-view" class="view">
                    <div class="profile-header">
                        <button class="back-btn" onclick="showView('home')">
                            <svg><use href="#icon-arrow-back"/></svg>
                        </button>
                        <h2>Profile</h2>
                        <button class="save-btn" onclick="saveProfile()">Save</button>
                    </div>
                    <div class="profile-content">
                        <!-- Profile form will be populated by JavaScript -->
                    </div>
                </div>

                <!-- Settings View -->
                <div id="settings-view" class="view">
                    <div class="settings-header">
                        <button class="back-btn" onclick="showView('home')">
                            <svg><use href="#icon-arrow-back"/></svg>
                        </button>
                        <h2>Settings</h2>
                    </div>
                    <div class="settings-content">
                        <!-- Settings form will be populated by JavaScript -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <!-- Scripts -->
    <script>
        console.log('HTML loaded successfully');
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded');
            const loginScreen = document.getElementById('login-screen');
            if (loginScreen) {
                console.log('Login screen found');
                console.log('Login screen classes:', loginScreen.className);
                const computedStyles = window.getComputedStyle(loginScreen);
                console.log('Login screen computed display:', computedStyles.display);
                console.log('Login screen computed opacity:', computedStyles.opacity);
                console.log('Login screen computed visibility:', computedStyles.visibility);
            }
        });
    </script>
    <script src="/js/app.js" onerror="console.error('Failed to load app.js')" onload="console.log('app.js loaded successfully')"></script>
</body>
</html>
