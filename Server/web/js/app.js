// Desktop Shake & Match App JavaScript
class ShakeMatchApp {
    constructor() {
        this.socket = null;
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.serverAddress = window.location.origin;
        
        this.init();
    }

    async init() {
        console.log('App initializing...');

        // Check if user is already logged in
        const token = localStorage.getItem('authToken');
        if (token) {
            try {
                await this.validateToken(token);
                this.showMainScreen();
            } catch (error) {
                console.error('Token validation failed:', error);
                localStorage.removeItem('authToken');
                this.showLoginScreen();
            }
        } else {
            console.log('No token found, showing login screen');
            this.showLoginScreen();
        }

        // Set up keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Set up socket connection if logged in
        if (this.user) {
            this.initializeSocket();
        }
    }

    showLoginScreen() {
        console.log('Showing login screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.add('active');
        mainScreen.classList.remove('active');

        // Debug login screen visibility
        console.log('Login screen classes:', loginScreen.className);
        const computedStyles = window.getComputedStyle(loginScreen);
        console.log('Login screen computed opacity:', computedStyles.opacity);
        console.log('Login screen computed visibility:', computedStyles.visibility);
        console.log('Login screen computed display:', computedStyles.display);
    }

    showMainScreen() {
        console.log('Showing main screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.remove('active');
        mainScreen.classList.add('active');
        this.showView('home');
        this.updateWelcomeText();
        this.updateProfileIcon();
        this.loadMatches();
    }

    async validateToken(token) {
        const response = await fetch(`${this.serverAddress}/api/validate-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Token validation failed');
        }

        const data = await response.json();
        this.user = data.user;
        this.userProfile = data.profile;
        this.updateWelcomeText();
        this.updateProfileIcon();
        return data;
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Spacebar for shake (when on home view)
            if (e.code === 'Space' && this.getCurrentView() === 'home') {
                e.preventDefault();
                this.handleShake();
            }
            
            // Escape to go back
            if (e.code === 'Escape') {
                const currentView = this.getCurrentView();
                if (currentView !== 'home') {
                    this.showView('home');
                }
            }
        });
    }

    getCurrentView() {
        const activeView = document.querySelector('.view.active');
        return activeView ? activeView.id.replace('-view', '') : 'home';
    }

    showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });
        
        // Show selected view
        document.getElementById(`${viewName}-view`).classList.add('active');
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            this.showAlert('Please enter both username and password');
            return;
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                localStorage.setItem('authToken', data.token);
                this.user = data.user;
                this.userProfile = data.profile;
                this.updateWelcomeText();
                this.updateProfileIcon();
                this.initializeSocket();
                this.showMainScreen();
            } else {
                this.showAlert(data.error || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Network error. Please try again.');
        }
    }

    async handleSocialLogin(provider) {
        // For now, show a message that social login is not implemented in desktop
        this.showAlert(`${provider} login will be implemented soon for desktop`);
    }

    initializeSocket() {
        if (this.socket) {
            this.socket.disconnect();
        }

        this.socket = io(this.serverAddress);
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            if (this.user) {
                this.socket.emit('user-online', { userId: this.user.id });
            }
        });

        this.socket.on('new-match', (data) => {
            console.log('New match received:', data);
            this.matches.push(data);
            this.renderMatches();
            this.showNotification('New match found!');
        });

        this.socket.on('new-message', (data) => {
            console.log('New message received:', data);
            if (this.currentChat && this.currentChat.userId === data.senderId) {
                this.addMessageToChat(data);
            }
            this.showNotification(`New message from ${data.senderName}`);
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
        });
    }

    async handleShake() {
        if (!this.user) return;

        const shakeBtn = document.getElementById('shake-btn');
        const searchingBubble = document.getElementById('searching-bubble');

        shakeBtn.classList.add('shaking');
        shakeBtn.disabled = true;
        searchingBubble.style.display = 'block';

        try {
            // Get user's location (for desktop, we'll use a default or ask for permission)
            const location = await this.getCurrentLocation();

            const response = await fetch(`${this.serverAddress}/api/shake`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    latitude: location.latitude,
                    longitude: location.longitude
                })
            });

            const data = await response.json();

            if (response.ok) {
                if (data.match) {
                    this.showNotification('Match found!');
                    this.matches.push(data.match);
                    this.renderMatches();
                } else {
                    this.showNotification('No matches found nearby. Try again later!');
                }
            } else {
                this.showAlert(data.error || 'Shake failed');
            }
        } catch (error) {
            console.error('Shake error:', error);
            this.showAlert('Failed to shake. Please try again.');
        } finally {
            setTimeout(() => {
                shakeBtn.classList.remove('shaking');
                shakeBtn.disabled = false;
                searchingBubble.style.display = 'none';
            }, 2000);
        }
    }

    async getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                // Fallback location (you might want to ask user to set this)
                resolve({ latitude: 40.7128, longitude: -74.0060 }); // NYC default
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                    });
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    // Fallback location
                    resolve({ latitude: 40.7128, longitude: -74.0060 });
                }
            );
        });
    }

    async loadMatches() {
        if (!this.user) return;

        try {
            const response = await fetch(`${this.serverAddress}/api/matches`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.matches = data.matches || [];
                this.renderMatches();
            }
        } catch (error) {
            console.error('Failed to load matches:', error);
        }
    }

    renderMatches() {
        const matchesList = document.getElementById('matches-list');

        if (this.matches.length === 0) {
            matchesList.innerHTML = `
                <div class="no-matches">
                    <svg class="no-matches-icon"><use href="#icon-heart"/></svg>
                    <p>No matches yet.</p>
                </div>
            `;
            return;
        }

        matchesList.innerHTML = this.matches.map(match => `
            <div class="match-card" data-user-id="${match.userId}">
                <div class="match-avatar" onclick="app.viewProfile('${match.userId}')">
                    ${match.profileImage ?
                        `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${match.username}">` :
                        match.username.charAt(0).toUpperCase()
                    }
                </div>
                <div class="match-info" onclick="app.viewProfile('${match.userId}')">
                    <h3>${match.username}</h3>
                    <p class="match-distance">${Math.round(match.distance * 10) / 10} km away</p>
                </div>
                <div class="match-actions">
                    <button class="chat-button" onclick="app.openChat('${match.userId}', '${match.username}')" title="Chat">
                        <svg><use href="#icon-chat"/></svg>
                    </button>
                </div>
            </div>
        `).join('');
    }

    openChat(userId, username) {
        this.currentChat = { userId, username };
        this.showView('chat');
        this.loadChatMessages(userId);
        
        // Update chat header
        document.querySelector('.chat-username').textContent = username;
        const chatAvatar = document.querySelector('.chat-avatar');
        const match = this.matches.find(m => m.userId === userId);
        if (match && match.profileImage) {
            chatAvatar.innerHTML = `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${username}">`;
        } else {
            chatAvatar.textContent = username.charAt(0).toUpperCase();
        }
    }

    async loadChatMessages(userId) {
        try {
            const response = await fetch(`${this.serverAddress}/api/messages/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.renderChatMessages(data.messages || []);
            }
        } catch (error) {
            console.error('Failed to load messages:', error);
        }
    }

    renderChatMessages(messages) {
        const chatMessages = document.getElementById('chat-messages');
        chatMessages.innerHTML = messages.map(message => `
            <div class="message ${message.senderId === this.user.id ? 'own' : 'other'}">
                <div class="message-bubble">
                    ${message.text}
                    <span class="message-time">${this.formatTime(message.timestamp)}</span>
                </div>
            </div>
        `).join('');
        
        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    showAlert(message) {
        // Simple alert for now - you could implement a custom modal
        alert(message);
    }

    showNotification(message) {
        // Simple notification - you could implement a toast system
        console.log('Notification:', message);

        // For now, just show a temporary message
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    addMessageToChat(message) {
        const chatMessages = document.getElementById('chat-messages');
        const messageElement = document.createElement('div');
        messageElement.className = `message ${message.senderId === this.user.id ? 'own' : 'other'}`;
        messageElement.innerHTML = `
            <div class="message-bubble">
                ${message.text}
                <span class="message-time">${this.formatTime(message.timestamp)}</span>
            </div>
        `;
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    viewProfile(userId) {
        // For now, just show an alert - full profile view will be implemented
        const match = this.matches.find(m => m.userId === userId);
        if (match) {
            this.showAlert(`Viewing profile for ${match.username}. Full profile view coming soon!`);
        }
    }

    updateWelcomeText() {
        const welcomeText = document.getElementById('welcome-text');
        if (welcomeText && this.user) {
            welcomeText.textContent = `Hi, ${this.user.username}!`;
        }
    }

    updateProfileIcon() {
        const profileBtn = document.getElementById('profile-btn');
        const profileIconContainer = profileBtn.querySelector('.profile-icon-container');

        if (!profileIconContainer) return;

        // Check if user has profile image
        const hasProfileImage = this.userProfile && this.userProfile.images && this.userProfile.images.length > 0;

        if (hasProfileImage) {
            const imageUri = this.userProfile.images[0].startsWith('data:')
                ? this.userProfile.images[0]
                : `data:image/jpeg;base64,${this.userProfile.images[0]}`;

            profileIconContainer.innerHTML = `<img src="${imageUri}" alt="Profile" class="profile-icon-image">`;
        } else {
            profileIconContainer.innerHTML = `<svg class="nav-icon default-profile-icon"><use href="#icon-user"/></svg>`;
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        if (this.socket) {
            this.socket.disconnect();
        }
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.showLoginScreen();
    }
}

// Initialize the app
const app = new ShakeMatchApp();

// Global functions for HTML onclick handlers
function handleLogin(event) {
    app.handleLogin(event);
}

function handleSocialLogin(provider) {
    app.handleSocialLogin(provider);
}

function showScreen(screenName) {
    if (screenName === 'profile' || screenName === 'settings') {
        app.showView(screenName);
    }
}

function showView(viewName) {
    app.showView(viewName);
}

function handleShake() {
    app.handleShake();
}

function handleChatKeyPress(event) {
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    }
}

function sendMessage() {
    const input = document.querySelector('.chat-input');
    const message = input.value.trim();
    
    if (!message || !app.currentChat) return;
    
    // Send message via socket
    app.socket.emit('send-message', {
        recipientId: app.currentChat.userId,
        text: message
    });
    
    // Add message to chat immediately
    app.addMessageToChat({
        senderId: app.user.id,
        text: message,
        timestamp: new Date().toISOString()
    });
    
    input.value = '';
}

function viewProfile() {
    if (app.currentChat) {
        app.viewProfile(app.currentChat.userId);
    }
}

function saveProfile() {
    // Profile saving functionality will be implemented
    app.showAlert('Profile saving will be implemented soon');
}
