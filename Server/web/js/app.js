// Desktop Shake & Match App JavaScript
class ShakeMatchApp {
    constructor() {
        this.socket = null;
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.serverAddress = window.location.origin;
        
        this.init();
    }

    async init() {
        console.log('App initializing...');

        // Check if user is already logged in
        const token = localStorage.getItem('authToken');
        if (token) {
            try {
                await this.validateToken(token);
                this.showMainScreen();
            } catch (error) {
                console.error('Token validation failed:', error);
                localStorage.removeItem('authToken');
                this.showLoginScreen();
            }
        } else {
            console.log('No token found, showing login screen');
            this.showLoginScreen();
        }

        // Set up keyboard shortcuts
        this.setupKeyboardShortcuts();

        // Set up socket connection if logged in
        if (this.user) {
            this.initializeSocket();
        }
    }

    showLoginScreen() {
        console.log('Showing login screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.add('active');
        mainScreen.classList.remove('active');

        // Debug login screen visibility
        console.log('Login screen classes:', loginScreen.className);
        const computedStyles = window.getComputedStyle(loginScreen);
        console.log('Login screen computed opacity:', computedStyles.opacity);
        console.log('Login screen computed visibility:', computedStyles.visibility);
        console.log('Login screen computed display:', computedStyles.display);
    }

    showMainScreen() {
        console.log('Showing main screen');
        const loginScreen = document.getElementById('login-screen');
        const mainScreen = document.getElementById('main-screen');

        loginScreen.classList.remove('active');
        mainScreen.classList.add('active');
        this.showView('home');
        this.updateWelcomeText();
        this.updateProfileIcon();
        this.loadMatches();
    }

    async validateToken(token) {
        const response = await fetch(`${this.serverAddress}/api/validate-token`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            }
        });

        if (!response.ok) {
            throw new Error('Token validation failed');
        }

        const data = await response.json();
        this.user = data.user;
        this.userProfile = data.profile;
        this.updateWelcomeText();
        this.updateProfileIcon();
        return data;
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Spacebar for shake (when on home view)
            if (e.code === 'Space' && this.getCurrentView() === 'home') {
                e.preventDefault();
                this.handleShake();
            }
            
            // Escape to go back
            if (e.code === 'Escape') {
                const currentView = this.getCurrentView();
                if (currentView !== 'home') {
                    this.showView('home');
                }
            }
        });
    }

    getCurrentView() {
        const activeView = document.querySelector('.view.active');
        return activeView ? activeView.id.replace('-view', '') : 'home';
    }

    showView(viewName) {
        // Hide all views
        document.querySelectorAll('.view').forEach(view => {
            view.classList.remove('active');
        });

        // Show selected view
        document.getElementById(`${viewName}-view`).classList.add('active');

        // Initialize view-specific content
        if (viewName === 'profile') {
            this.initializeProfileView();
        } else if (viewName === 'settings') {
            this.initializeSettingsView();
        }
    }

    async handleLogin(event) {
        event.preventDefault();
        
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        if (!username || !password) {
            this.showAlert('Please enter both username and password');
            return;
        }

        try {
            const response = await fetch(`${this.serverAddress}/api/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ username, password })
            });

            const data = await response.json();

            if (response.ok) {
                localStorage.setItem('authToken', data.token);
                this.user = data.user;
                this.userProfile = data.profile;
                this.updateWelcomeText();
                this.updateProfileIcon();
                this.initializeSocket();
                this.showMainScreen();
            } else {
                this.showAlert(data.error || 'Login failed');
            }
        } catch (error) {
            console.error('Login error:', error);
            this.showAlert('Network error. Please try again.');
        }
    }

    async handleSocialLogin(provider) {
        // For now, show a message that social login is not implemented in desktop
        this.showAlert(`${provider} login will be implemented soon for desktop`);
    }

    initializeSocket() {
        if (this.socket) {
            this.socket.disconnect();
        }

        this.socket = io(this.serverAddress);
        
        this.socket.on('connect', () => {
            console.log('Connected to server');
            if (this.user) {
                this.socket.emit('register', {
                    userId: this.user.id,
                    username: this.user.username
                });
            }
        });

        this.socket.on('new-match', (data) => {
            console.log('New match received:', data);
            this.matches.push(data);
            this.renderMatches();
            this.showNotification('New match found!');
        });

        this.socket.on('message', (data) => {
            console.log('New message received:', data);
            if (this.currentChat && this.currentChat.userId === data.senderId) {
                this.addMessageToChat(data);
            }
            this.showNotification(`New message from ${data.senderUsername}`);
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from server');
        });
    }

    async handleShake() {
        if (!this.user) return;

        const shakeBtn = document.getElementById('shake-btn');
        const searchingBubble = document.getElementById('searching-bubble');

        shakeBtn.classList.add('shaking');
        shakeBtn.disabled = true;
        searchingBubble.style.display = 'block';

        try {
            // Get user's location (for desktop, we'll use a default or ask for permission)
            const location = await this.getCurrentLocation();

            const response = await fetch(`${this.serverAddress}/api/shake`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                },
                body: JSON.stringify({
                    latitude: location.latitude,
                    longitude: location.longitude
                })
            });

            const data = await response.json();

            if (response.ok) {
                if (data.match) {
                    this.showNotification('Match found!');
                    this.matches.push(data.match);
                    this.renderMatches();
                } else {
                    this.showNotification('No matches found nearby. Try again later!');
                }
            } else {
                this.showAlert(data.error || 'Shake failed');
            }
        } catch (error) {
            console.error('Shake error:', error);
            this.showAlert('Failed to shake. Please try again.');
        } finally {
            setTimeout(() => {
                shakeBtn.classList.remove('shaking');
                shakeBtn.disabled = false;
                searchingBubble.style.display = 'none';
            }, 2000);
        }
    }

    async getCurrentLocation() {
        return new Promise((resolve, reject) => {
            if (!navigator.geolocation) {
                // Fallback location (you might want to ask user to set this)
                resolve({ latitude: 40.7128, longitude: -74.0060 }); // NYC default
                return;
            }

            navigator.geolocation.getCurrentPosition(
                (position) => {
                    resolve({
                        latitude: position.coords.latitude,
                        longitude: position.coords.longitude
                    });
                },
                (error) => {
                    console.error('Geolocation error:', error);
                    // Fallback location
                    resolve({ latitude: 40.7128, longitude: -74.0060 });
                }
            );
        });
    }

    async loadMatches() {
        if (!this.user) return;

        try {
            const response = await fetch(`${this.serverAddress}/api/matches`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.matches = data.matches || [];
                this.renderMatches();
            }
        } catch (error) {
            console.error('Failed to load matches:', error);
        }
    }

    renderMatches() {
        const matchesList = document.getElementById('matches-list');

        if (this.matches.length === 0) {
            matchesList.innerHTML = `
                <div class="no-matches">
                    <svg class="no-matches-icon"><use href="#icon-heart"/></svg>
                    <p>No matches yet.</p>
                </div>
            `;
            return;
        }

        matchesList.innerHTML = this.matches.map(match => `
            <div class="match-card" data-user-id="${match.userId}">
                <div class="match-avatar" onclick="app.viewProfile('${match.userId}')">
                    ${match.profileImage ?
                        `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${match.username}">` :
                        match.username.charAt(0).toUpperCase()
                    }
                </div>
                <div class="match-info" onclick="app.viewProfile('${match.userId}')">
                    <h3>${match.username}</h3>
                    <p class="match-distance">${Math.round(match.distance * 10) / 10} km away</p>
                </div>
                <div class="match-actions">
                    <button class="chat-button" onclick="app.openChat('${match.userId}', '${match.username}')" title="Chat">
                        <svg><use href="#icon-chat"/></svg>
                    </button>
                </div>
            </div>
        `).join('');
    }

    openChat(userId, username) {
        this.currentChat = { userId, username };
        this.showView('chat');
        this.loadChatMessages(userId);

        // Update chat header
        document.getElementById('chat-username').textContent = username;
        const chatAvatar = document.getElementById('chat-avatar');
        const match = this.matches.find(m => m.userId === userId);
        if (match && match.profileImage) {
            chatAvatar.innerHTML = `<img src="data:image/jpeg;base64,${match.profileImage}" alt="${username}">`;
        } else {
            chatAvatar.textContent = username.charAt(0).toUpperCase();
        }

        // Clear empty state
        const emptyState = document.querySelector('.chat-empty-state');
        if (emptyState) {
            emptyState.style.display = 'none';
        }
    }

    async loadChatMessages(userId) {
        try {
            const response = await fetch(`${this.serverAddress}/api/messages/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.renderChatMessages(data.messages || []);
            }
        } catch (error) {
            console.error('Failed to load messages:', error);
        }
    }

    renderChatMessages(messages) {
        const chatMessages = document.getElementById('chat-messages');

        if (messages.length === 0) {
            chatMessages.innerHTML = `
                <div class="chat-empty-state">
                    <p>Start your conversation!</p>
                </div>
            `;
            return;
        }

        chatMessages.innerHTML = messages.map(message => {
            // Handle both senderId (string) and senderId (ObjectId) formats
            const isOwnMessage = message.senderId === this.user.id || message.senderId.toString() === this.user.id;
            return `
                <div class="message ${isOwnMessage ? 'own' : 'other'}">
                    <div class="message-bubble">
                        ${message.text}
                        <span class="message-time">${this.formatTime(message.timestamp)}</span>
                    </div>
                </div>
            `;
        }).join('');

        // Scroll to bottom
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    formatTime(timestamp) {
        const date = new Date(timestamp);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }

    showAlert(message) {
        // Simple alert for now - you could implement a custom modal
        alert(message);
    }

    showNotification(message) {
        // Simple notification - you could implement a toast system
        console.log('Notification:', message);

        // For now, just show a temporary message
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--primary-color);
            color: white;
            padding: 1rem 1.5rem;
            border-radius: var(--border-radius);
            box-shadow: var(--shadow-lg);
            z-index: 1000;
            animation: fadeIn 0.3s ease-out;
        `;
        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    addMessageToChat(message) {
        const chatMessages = document.getElementById('chat-messages');

        // Remove empty state if it exists
        const emptyState = chatMessages.querySelector('.chat-empty-state');
        if (emptyState) {
            emptyState.remove();
        }

        const messageElement = document.createElement('div');
        // Handle both senderId (string) and senderId (ObjectId) formats
        const isOwnMessage = message.senderId === this.user.id || message.senderId.toString() === this.user.id;
        messageElement.className = `message ${isOwnMessage ? 'own' : 'other'}`;
        messageElement.innerHTML = `
            <div class="message-bubble">
                ${message.text}
                <span class="message-time">${this.formatTime(message.timestamp)}</span>
            </div>
        `;
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    async viewProfile(userId) {
        const match = this.matches.find(m => m.userId === userId);
        if (!match) {
            this.showAlert('User not found');
            return;
        }

        this.currentProfileUser = match;
        this.currentProfileImages = [];
        this.currentImageIndex = 0;

        // Show modal
        const modal = document.getElementById('profile-modal');
        modal.style.display = 'flex';
        setTimeout(() => modal.classList.add('active'), 10);

        // Set basic info
        document.getElementById('profile-modal-title').textContent = match.username;
        document.getElementById('profile-modal-name').textContent = match.username;

        // Load profile data
        await this.loadUserProfile(userId, match.username);
    }

    async loadUserProfile(userId, username) {
        try {
            const response = await fetch(`${this.serverAddress}/api/profile/${userId}`, {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('authToken')}`
                }
            });

            if (response.ok) {
                const data = await response.json();
                this.displayProfileData(data.profile, username);
            } else {
                // Show basic profile with just username
                this.displayProfileData({ username }, username);
            }
        } catch (error) {
            console.error('Error loading profile:', error);
            this.displayProfileData({ username }, username);
        }
    }

    displayProfileData(profile, username) {
        // Update name with age if available
        const nameText = profile.age ? `${username}, ${profile.age}` : username;
        document.getElementById('profile-modal-name').textContent = nameText;

        // Handle images
        const photoContainer = document.getElementById('profile-photo-container');
        const photoNav = document.getElementById('photo-nav');

        if (profile.images && profile.images.length > 0) {
            this.currentProfileImages = profile.images;
            this.displayProfileImage(0);

            if (profile.images.length > 1) {
                photoNav.style.display = 'flex';
                this.updatePhotoIndicators();
            } else {
                photoNav.style.display = 'none';
            }
        } else {
            // Show avatar
            const placeholder = document.getElementById('profile-photo-placeholder');
            const avatarText = document.getElementById('profile-avatar-text');
            avatarText.textContent = username.charAt(0).toUpperCase();
            photoNav.style.display = 'none';
        }

        // Handle description
        const descriptionSection = document.getElementById('profile-description-section');
        const descriptionText = document.getElementById('profile-modal-description');

        if (profile.description) {
            descriptionText.textContent = profile.description;
            descriptionSection.style.display = 'block';
        } else {
            descriptionText.textContent = 'No description available';
            descriptionSection.style.display = 'block';
        }

        // Handle passions
        const passionsSection = document.getElementById('profile-passions-section');
        const passionTags = document.getElementById('profile-passion-tags');

        if (profile.passions && profile.passions.length > 0) {
            passionTags.innerHTML = profile.passions.map(passion =>
                `<span class="profile-passion-tag">${passion}</span>`
            ).join('');
            passionsSection.style.display = 'block';
        } else {
            passionsSection.style.display = 'none';
        }
    }

    displayProfileImage(index) {
        if (!this.currentProfileImages || this.currentProfileImages.length === 0) return;

        const photoContainer = document.getElementById('profile-photo-container');
        const image = this.currentProfileImages[index];
        const imageUri = image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}`;

        photoContainer.innerHTML = `<img src="${imageUri}" class="profile-photo" alt="Profile photo">`;
        this.currentImageIndex = index;
        this.updatePhotoIndicators();
    }

    updatePhotoIndicators() {
        const indicators = document.getElementById('photo-indicators');
        if (!this.currentProfileImages || this.currentProfileImages.length <= 1) {
            indicators.innerHTML = '';
            return;
        }

        indicators.innerHTML = this.currentProfileImages.map((_, index) =>
            `<div class="photo-indicator ${index === this.currentImageIndex ? 'active' : ''}" onclick="app.displayProfileImage(${index})"></div>`
        ).join('');
    }

    updateWelcomeText() {
        const welcomeText = document.getElementById('welcome-text');
        if (welcomeText && this.user) {
            welcomeText.textContent = `Hi, ${this.user.username}!`;
        }
    }

    updateProfileIcon() {
        const profileBtn = document.getElementById('profile-btn');
        const profileIconContainer = profileBtn.querySelector('.profile-icon-container');

        if (!profileIconContainer) return;

        // Check if user has profile image
        const hasProfileImage = this.userProfile && this.userProfile.images && this.userProfile.images.length > 0;

        if (hasProfileImage) {
            const imageUri = this.userProfile.images[0].startsWith('data:')
                ? this.userProfile.images[0]
                : `data:image/jpeg;base64,${this.userProfile.images[0]}`;

            profileIconContainer.innerHTML = `<img src="${imageUri}" alt="Profile" class="profile-icon-image">`;
        } else {
            profileIconContainer.innerHTML = `<svg class="nav-icon default-profile-icon"><use href="#icon-user"/></svg>`;
        }
    }

    initializeProfileView() {
        // Populate age dropdown
        const ageSelect = document.getElementById('profile-age');
        if (ageSelect && ageSelect.children.length <= 1) {
            for (let age = 18; age <= 120; age++) {
                const option = document.createElement('option');
                option.value = age;
                option.textContent = age;
                ageSelect.appendChild(option);
            }
        }

        // Initialize passions
        this.initializePassions();

        // Load current profile data
        this.loadProfileData();
    }

    initializePassions() {
        const passions = [
            'Travel', 'Music', 'Movies', 'Sports', 'Reading', 'Cooking', 'Photography',
            'Art', 'Dancing', 'Hiking', 'Gaming', 'Fitness', 'Yoga', 'Coffee',
            'Wine', 'Fashion', 'Technology', 'Animals', 'Nature', 'Beach',
            'Mountains', 'Food', 'Adventure', 'Learning', 'Writing'
        ];

        const container = document.getElementById('passions-container');
        container.innerHTML = passions.map(passion => `
            <div class="passion-chip" onclick="togglePassion('${passion}')" data-passion="${passion}">
                ${passion}
            </div>
        `).join('');
    }

    async loadProfileData() {
        if (!this.userProfile) return;

        // Load age
        const ageSelect = document.getElementById('profile-age');
        if (this.userProfile.age) {
            ageSelect.value = this.userProfile.age;
        }

        // Load description
        const descriptionInput = document.getElementById('profile-description');
        if (this.userProfile.description) {
            descriptionInput.value = this.userProfile.description;
        }

        // Load images
        if (this.userProfile.images) {
            this.userProfile.images.forEach((image, index) => {
                if (image && index < 4) {
                    const placeholder = document.getElementById(`photo-placeholder-${index}`);
                    const imageUri = image.startsWith('data:') ? image : `data:image/jpeg;base64,${image}`;
                    placeholder.innerHTML = `<img src="${imageUri}" class="photo-preview" alt="Profile photo ${index + 1}">`;
                }
            });
        }

        // Load passions
        if (this.userProfile.passions) {
            this.userProfile.passions.forEach(passion => {
                const chip = document.querySelector(`[data-passion="${passion}"]`);
                if (chip) {
                    chip.classList.add('selected');
                }
            });
            this.updatePassionCount();
        }
    }

    initializeSettingsView() {
        // Initialize settings with current values
        this.loadSettingsData();
    }

    async loadSettingsData() {
        // Load current settings from localStorage or defaults
        const maxDistance = localStorage.getItem('maxDistance') || '25';
        const minAge = localStorage.getItem('minAge') || '18';
        const maxAge = localStorage.getItem('maxAge') || '100';
        const notificationsEnabled = localStorage.getItem('notificationsEnabled') !== 'false';

        // Update UI
        document.getElementById('distance-slider').value = maxDistance;
        document.getElementById('distance-value').textContent = `${maxDistance} km`;

        document.getElementById('min-age-slider').value = minAge;
        document.getElementById('min-age-value').textContent = `${minAge} years`;

        document.getElementById('max-age-slider').value = maxAge;
        document.getElementById('max-age-value').textContent = `${maxAge} years`;

        document.getElementById('notifications-toggle').checked = notificationsEnabled;

        // Check location permission
        this.checkLocationPermissionStatus();
    }

    async checkLocationPermissionStatus() {
        const statusElement = document.getElementById('location-status');
        const buttonElement = document.getElementById('location-btn');

        if (!navigator.geolocation) {
            statusElement.textContent = 'Not supported';
            buttonElement.style.display = 'none';
            return;
        }

        try {
            const position = await new Promise((resolve, reject) => {
                navigator.geolocation.getCurrentPosition(resolve, reject, { timeout: 5000 });
            });
            statusElement.textContent = 'Enabled';
            buttonElement.textContent = 'Enabled';
            buttonElement.disabled = true;
        } catch (error) {
            statusElement.textContent = 'Disabled';
            buttonElement.textContent = 'Enable';
            buttonElement.disabled = false;
        }
    }

    logout() {
        localStorage.removeItem('authToken');
        if (this.socket) {
            this.socket.disconnect();
        }
        this.user = null;
        this.userProfile = null;
        this.matches = [];
        this.currentChat = null;
        this.showLoginScreen();
    }
}

// Initialize the app
const app = new ShakeMatchApp();

// Global functions for HTML onclick handlers
function handleLogin(event) {
    app.handleLogin(event);
}

function handleSocialLogin(provider) {
    app.handleSocialLogin(provider);
}

function showScreen(screenName) {
    if (screenName === 'profile' || screenName === 'settings') {
        app.showView(screenName);
    }
}

function showView(viewName) {
    app.showView(viewName);
}

function handleShake() {
    app.handleShake();
}

function handleChatKeyPress(event) {
    const textarea = event.target;

    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        sendMessage();
    } else {
        // Auto-resize textarea
        setTimeout(() => {
            textarea.style.height = 'auto';
            textarea.style.height = Math.min(textarea.scrollHeight, 100) + 'px';
        }, 0);
    }
}

function sendMessage() {
    const input = document.getElementById('chat-input');
    const sendBtn = document.getElementById('send-btn');
    const message = input.value.trim();

    if (!message || !app.currentChat || !app.user) return;

    // Disable send button temporarily
    sendBtn.disabled = true;

    // Create message data matching server expectations
    const messageData = {
        id: Date.now().toString(), // Simple ID generation
        senderId: app.user.id,
        senderUsername: app.user.username,
        receiverId: app.currentChat.userId,
        receiverUsername: app.currentChat.username,
        text: message,
        timestamp: new Date().toISOString()
    };

    // Send message via socket with correct event name
    app.socket.emit('sendMessage', messageData);

    // Add message to chat immediately
    app.addMessageToChat({
        senderId: app.user.id,
        text: message,
        timestamp: messageData.timestamp
    });

    input.value = '';
    input.style.height = 'auto'; // Reset textarea height

    // Re-enable send button
    setTimeout(() => {
        sendBtn.disabled = false;
    }, 500);
}

function viewProfile() {
    if (app.currentChat) {
        app.viewProfile(app.currentChat.userId);
    }
}

// Profile functions
function selectPhoto(index) {
    document.getElementById(`photo-${index}`).click();
}

function handlePhotoSelect(index, event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        const placeholder = document.getElementById(`photo-placeholder-${index}`);
        placeholder.innerHTML = `<img src="${e.target.result}" class="photo-preview" alt="Profile photo ${index + 1}">`;
    };
    reader.readAsDataURL(file);
}

function togglePassion(passion) {
    const chip = document.querySelector(`[data-passion="${passion}"]`);
    const isSelected = chip.classList.contains('selected');
    const selectedCount = document.querySelectorAll('.passion-chip.selected').length;

    if (isSelected) {
        chip.classList.remove('selected');
    } else if (selectedCount < 6) {
        chip.classList.add('selected');
    } else {
        app.showAlert('You can select maximum 6 passions');
        return;
    }

    updatePassionCount();
}

function updatePassionCount() {
    const selectedCount = document.querySelectorAll('.passion-chip.selected').length;
    const countElement = document.getElementById('passion-count');
    const statusElement = document.querySelector('.passion-status');

    countElement.textContent = `${selectedCount}/6 passions selected`;

    if (selectedCount < 3) {
        countElement.textContent += ` (need ${3 - selectedCount} more)`;
        statusElement.className = 'passion-status warning';
    } else {
        statusElement.className = 'passion-status success';
    }
}

async function saveProfile() {
    const saveBtn = document.getElementById('profile-save-btn');
    saveBtn.disabled = true;
    saveBtn.textContent = 'Saving...';

    try {
        // Collect form data
        const age = document.getElementById('profile-age').value;
        const description = document.getElementById('profile-description').value;
        const selectedPassions = Array.from(document.querySelectorAll('.passion-chip.selected'))
            .map(chip => chip.dataset.passion);

        // Collect images
        const images = [];
        for (let i = 0; i < 4; i++) {
            const img = document.querySelector(`#photo-placeholder-${i} img`);
            if (img) {
                images.push(img.src);
            }
        }

        // Validate required fields
        if (!description.trim()) {
            app.showAlert('Description is required');
            return;
        }

        if (!age) {
            app.showAlert('Age is required');
            return;
        }

        if (selectedPassions.length < 3) {
            app.showAlert('Please select at least 3 passions');
            return;
        }

        if (images.length === 0) {
            app.showAlert('Please add at least one photo');
            return;
        }

        // Save profile
        const response = await fetch(`${app.serverAddress}/api/profile`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('authToken')}`
            },
            body: JSON.stringify({
                age: parseInt(age),
                description: description.trim(),
                passions: selectedPassions,
                images: images
            })
        });

        if (response.ok) {
            const data = await response.json();
            app.userProfile = data.profile;
            app.updateProfileIcon();
            app.showNotification('Profile updated successfully!');
        } else {
            const error = await response.json();
            app.showAlert(error.error || 'Failed to save profile');
        }
    } catch (error) {
        console.error('Profile save error:', error);
        app.showAlert('Failed to save profile. Please try again.');
    } finally {
        saveBtn.disabled = false;
        saveBtn.textContent = 'Save';
    }
}

// Settings functions
function updateDistance(value) {
    document.getElementById('distance-value').textContent = `${value} km`;
    localStorage.setItem('maxDistance', value);
}

function updateMinAge(value) {
    document.getElementById('min-age-value').textContent = `${value} years`;
    localStorage.setItem('minAge', value);

    // Ensure max age is not less than min age
    const maxAgeSlider = document.getElementById('max-age-slider');
    if (parseInt(maxAgeSlider.value) < parseInt(value)) {
        maxAgeSlider.value = value;
        updateMaxAge(value);
    }
}

function updateMaxAge(value) {
    document.getElementById('max-age-value').textContent = `${value} years`;
    localStorage.setItem('maxAge', value);

    // Ensure min age is not greater than max age
    const minAgeSlider = document.getElementById('min-age-slider');
    if (parseInt(minAgeSlider.value) > parseInt(value)) {
        minAgeSlider.value = value;
        updateMinAge(value);
    }
}

function toggleNotifications(enabled) {
    localStorage.setItem('notificationsEnabled', enabled);
    if (enabled) {
        app.showNotification('Notifications enabled');
    } else {
        app.showNotification('Notifications disabled');
    }
}

async function requestLocationPermission() {
    try {
        const position = await new Promise((resolve, reject) => {
            navigator.geolocation.getCurrentPosition(resolve, reject);
        });
        app.checkLocationPermissionStatus();
        app.showNotification('Location permission granted');
    } catch (error) {
        app.showAlert('Location permission denied. Please enable it in your browser settings.');
    }
}

// Chat functions
function showChatOptions() {
    if (!app.currentChat) return;

    const options = [
        'View Profile',
        'Block User',
        'Delete Chat'
    ];

    // Simple implementation - you could create a proper modal
    const choice = prompt('Choose an option:\n1. View Profile\n2. Block User\n3. Delete Chat\n\nEnter number (1-3):');

    switch (choice) {
        case '1':
            viewProfile();
            break;
        case '2':
            if (confirm(`Are you sure you want to block ${app.currentChat.username}?`)) {
                // Implement block functionality
                app.showAlert('Block functionality will be implemented');
            }
            break;
        case '3':
            if (confirm(`Are you sure you want to delete this chat with ${app.currentChat.username}?`)) {
                // Implement delete chat functionality
                app.showAlert('Delete chat functionality will be implemented');
            }
            break;
    }
}

// Profile Modal Functions
function closeProfileModal() {
    const modal = document.getElementById('profile-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function nextProfilePhoto() {
    if (!app.currentProfileImages || app.currentProfileImages.length <= 1) return;
    const nextIndex = (app.currentImageIndex + 1) % app.currentProfileImages.length;
    app.displayProfileImage(nextIndex);
}

function prevProfilePhoto() {
    if (!app.currentProfileImages || app.currentProfileImages.length <= 1) return;
    const prevIndex = app.currentImageIndex === 0 ? app.currentProfileImages.length - 1 : app.currentImageIndex - 1;
    app.displayProfileImage(prevIndex);
}

function showProfileOptions() {
    if (!app.currentProfileUser) return;

    // Simple implementation for now
    const options = [
        'Block User',
        'Report User'
    ];

    const choice = confirm(`Block ${app.currentProfileUser.username}?`);
    if (choice) {
        app.showAlert('Block functionality will be implemented');
    }
}

// Chat Options Modal Functions
function showChatOptions() {
    if (!app.currentChat) return;

    const modal = document.getElementById('chat-options-modal');
    modal.style.display = 'flex';
    setTimeout(() => modal.classList.add('active'), 10);
}

function closeChatOptionsModal() {
    const modal = document.getElementById('chat-options-modal');
    modal.classList.remove('active');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

function viewProfileFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        app.viewProfile(app.currentChat.userId);
    }
}

function blockUserFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        if (confirm(`Are you sure you want to block ${app.currentChat.username}?`)) {
            app.showAlert('Block functionality will be implemented');
        }
    }
}

function deleteChatFromChat() {
    closeChatOptionsModal();
    if (app.currentChat) {
        if (confirm(`Are you sure you want to delete this chat with ${app.currentChat.username}?`)) {
            app.showAlert('Delete chat functionality will be implemented');
        }
    }
}

// Close modals when clicking outside
document.addEventListener('click', function(event) {
    const profileModal = document.getElementById('profile-modal');
    const chatOptionsModal = document.getElementById('chat-options-modal');

    if (event.target === profileModal) {
        closeProfileModal();
    }

    if (event.target === chatOptionsModal) {
        closeChatOptionsModal();
    }
});

function logout() {
    if (confirm('Are you sure you want to logout?')) {
        app.logout();
    }
}
