/* Desktop Shake & Match App Styles */
:root {
    --primary-color: #4e9af1;
    --primary-dark: #3a7bc8;
    --secondary-color: #f8f9fa;
    --accent-color: #ff6b6b;
    --text-primary: #2c3e50;
    --text-secondary: #7f8c8d;
    --text-light: #bdc3c7;
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --border-color: #dee2e6;
    --shadow-sm: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-md: 0 4px 12px rgba(0,0,0,0.15);
    --shadow-lg: 0 8px 24px rgba(0,0,0,0.2);
    --border-radius: 12px;
    --border-radius-lg: 20px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON>e UI', <PERSON><PERSON>, sans-serif;
    background: #f8f9fa;
    min-height: 100vh;
    color: var(--text-primary);
    overflow: hidden;
}

.app-container {
    width: 100vw;
    height: 100vh;
    position: relative;
}

/* Loading Screen */
.loading-screen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100% !important;
    height: 100% !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    z-index: 1000 !important;
    opacity: 1 !important;
    visibility: visible !important;
}

.loading-content {
    text-align: center;
    color: white;
}

.logo-container {
    margin-bottom: 2rem;
}

.logo-icon {
    width: 80px;
    height: 80px;
    background: white;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--shadow-lg);
}

.logo-icon::before {
    content: '💕';
    font-size: 2rem;
}

.loading-content h1 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255,255,255,0.3);
    border-top: 4px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 2rem auto 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Screen Management */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    pointer-events: none;
}

.screen.active {
    opacity: 1;
    visibility: visible;
    pointer-events: auto;
}

/* Login Screen */
.login-container {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    pointer-events: auto;
    z-index: 1;
}

.login-content {
    background: white;
    padding: 3rem;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-lg);
    width: 100%;
    max-width: 450px;
    text-align: center;
    pointer-events: auto;
    position: relative;
    z-index: 2;
}

.logo-section {
    margin-bottom: 2.5rem;
}

.logo-section h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin: 1rem 0 0.5rem;
}

.logo-section p {
    color: var(--text-secondary);
    font-size: 1.1rem;
}

.social-login {
    margin-bottom: 2rem;
}

.social-btn {
    width: 100%;
    padding: 1rem;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.75rem;
    pointer-events: auto;
    position: relative;
    z-index: 3;
}

.social-icon {
    width: 20px;
    height: 20px;
}

.google-btn {
    background: #4285f4;
    color: white;
}

.google-btn:hover {
    background: #357ae8;
}

.facebook-btn {
    background: #1877f2;
    color: white;
}

.facebook-btn:hover {
    background: #166fe5;
}

.apple-btn {
    background: #000;
    color: white;
}

.apple-btn:hover {
    background: #333;
}

.divider {
    position: relative;
    margin: 2rem 0;
    text-align: center;
}

.divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--border-color);
}

.divider span {
    background: white;
    padding: 0 1rem;
    color: var(--text-secondary);
}

.form-group {
    margin-bottom: 1rem;
}

.form-group input {
    width: 100%;
    padding: 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    pointer-events: auto;
    cursor: text;
    z-index: 3;
    position: relative;
}

.form-group input:focus {
    outline: none;
    border-color: var(--primary-color);
}

.login-btn {
    width: 100%;
    padding: 1rem;
    background: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    pointer-events: auto;
    position: relative;
    z-index: 3;
}

.login-btn:hover {
    background: var(--primary-dark);
}

/* Main App Screen */
#main-screen {
    background: #f8f9fa;
    position: relative;
}

/* Floating Shapes */
.floating-shape {
    position: absolute;
    border-radius: 50%;
    z-index: 0;
    pointer-events: none;
}

.floating-shape-1 {
    width: 180px;
    height: 180px;
    top: -40px;
    right: -40px;
    background: rgba(245, 245, 245, 0.7);
    border: 1px solid #e0e0e0;
    animation: float1 15s ease-in-out infinite;
}

.floating-shape-2 {
    width: 220px;
    height: 220px;
    top: 50%;
    left: -110px;
    background: rgba(245, 245, 245, 0.5);
    border: 1px solid #e8e8e8;
    animation: float2 18s ease-in-out infinite;
}

.floating-shape-3 {
    width: 150px;
    height: 150px;
    bottom: 100px;
    right: -30px;
    background: rgba(245, 245, 245, 0.6);
    border: 1px solid #e8e8e8;
    animation: float3 12s ease-in-out infinite;
}

@keyframes float1 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(20px, -30px) rotate(11.25deg); }
    50% { transform: translate(-20px, 30px) rotate(22.5deg); }
    75% { transform: translate(20px, 30px) rotate(33.75deg); }
}

@keyframes float2 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-30px, -40px) rotate(-15deg); }
    50% { transform: translate(30px, 40px) rotate(-30deg); }
    75% { transform: translate(-30px, 40px) rotate(-45deg); }
}

@keyframes float3 {
    0%, 100% { transform: translate(0, 0) rotate(0deg); }
    25% { transform: translate(-15px, -25px) rotate(22.5deg); }
    50% { transform: translate(15px, 25px) rotate(45deg); }
    75% { transform: translate(-15px, 25px) rotate(67.5deg); }
}

.app-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    max-width: 1200px;
    margin: 0 auto;
}

.app-title {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.nav-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-btn:hover {
    background: var(--bg-secondary);
}

.nav-icon {
    width: 28px;
    height: 28px;
    color: #555;
}

.profile-icon-container {
    width: 28px;
    height: 28px;
    border-radius: 14px;
    overflow: hidden;
    background: #f0f0f0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-icon-image {
    width: 100%;
    height: 100%;
    border-radius: 14px;
    object-fit: cover;
}

.default-profile-icon {
    width: 20px;
    height: 20px;
}

.main-content {
    height: calc(100vh - 80px);
    overflow: hidden;
    position: relative;
    z-index: 1;
}

/* View Management */
.view {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: var(--transition);
    overflow-y: auto;
}

.view.active {
    opacity: 1;
    visibility: visible;
}

/* Home View */
#home-view {
    display: flex;
    flex-direction: column;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    height: 100%;
    position: relative;
}

/* Welcome Section */
.welcome-section {
    padding: 25px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.welcome-text {
    font-size: 26px;
    font-weight: 700;
    margin-bottom: 12px;
    color: #333;
}

.instruction-text {
    font-size: 16px;
    color: #666;
    line-height: 22px;
}

/* Shake Section */
.shake-section {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    position: relative;
    z-index: 2;
}

.shake-container {
    text-align: center;
}

.shake-outer-circle {
    width: 180px;
    height: 180px;
    border-radius: 90px;
    background: #f8f8f8;
    display: flex;
    justify-content: center;
    align-items: center;
    border: 1.5px solid #e0e0e0;
    box-shadow: 0 6px 8px rgba(0,0,0,0.1);
    margin: 0 auto 20px;
    transition: transform 0.3s ease;
}

.shake-button {
    width: 150px;
    height: 150px;
    border-radius: 75px;
    background: #4e9af1;
    border: none;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    box-shadow: 0 4px 6px rgba(78, 154, 241, 0.3);
}

.shake-button:hover .shake-outer-circle {
    transform: scale(1.05);
}

.shake-button:active .shake-outer-circle {
    transform: scale(0.95);
}

.shake-button.shaking .shake-outer-circle {
    animation: shake 0.5s ease-in-out;
}

.shake-icon {
    width: 50px;
    height: 50px;
}

.shake-text {
    font-size: 16px;
    font-weight: 700;
    letter-spacing: 1.5px;
}

.bubble-container {
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.searching-bubble {
    background: rgba(78, 154, 241, 0.1);
    padding: 10px 20px;
    border-radius: 30px;
    border: 1px solid rgba(78, 154, 241, 0.3);
}

.searching-text {
    font-size: 16px;
    color: #4e9af1;
    font-weight: 500;
    margin: 0;
}

/* Matches Section */
.matches-section {
    flex: 1;
    background: transparent;
    margin: 0 20px 20px;
    padding: 0;
    position: relative;
    z-index: 2;
}

.section-title {
    font-size: 22px;
    font-weight: 700;
    margin-bottom: 15px;
    color: #333;
    margin-top: 5px;
    margin-left: 5px;
}

.matches-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.no-matches {
    text-align: center;
    padding: 40px;
    border-radius: 15px;
    background: #f8fafc;
    margin-top: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    color: #999;
}

.no-matches-icon {
    width: 48px;
    height: 48px;
    margin-bottom: 15px;
    opacity: 0.5;
    color: #ccc;
}

.no-matches p {
    font-size: 16px;
    line-height: 22px;
    margin: 0;
}

.match-card {
    display: flex;
    align-items: center;
    padding: 15px;
    background: #f8fafc;
    border-radius: 15px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    transition: var(--transition);
    cursor: pointer;
    border: 1px solid transparent;
}

.match-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.match-avatar {
    width: 50px;
    height: 50px;
    border-radius: 25px;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: 700;
    color: white;
    margin-right: 15px;
    overflow: hidden;
    box-shadow: 0 3px 4px rgba(78, 154, 241, 0.2);
}

.match-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 25px;
}

.match-info {
    flex: 1;
}

.match-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c384a;
    margin: 0 0 4px 0;
}

.match-distance {
    font-size: 14px;
    color: #8a9cb0;
    margin: 0;
}

.match-actions {
    display: flex;
    gap: 10px;
}

.chat-button {
    width: 45px;
    height: 45px;
    border-radius: 22.5px;
    background: #f0f8ff;
    border: 1px solid #e8eef4;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: var(--transition);
}

.chat-button:hover {
    background: #e8f4ff;
}

.chat-button svg {
    width: 24px;
    height: 24px;
    color: #4e9af1;
}

/* Chat View */
#chat-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.chat-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.back-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    background: var(--bg-secondary);
}

.back-btn svg {
    width: 24px;
    height: 24px;
    color: #555;
}

.chat-user-info {
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    flex: 1;
    margin-left: 12px;
}

.chat-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
    color: white;
    overflow: hidden;
    font-size: 18px;
}

.chat-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.chat-username {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.chat-header-actions {
    display: flex;
    align-items: center;
}

.chat-action-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.chat-action-btn:hover {
    background: var(--bg-secondary);
}

.chat-action-btn svg {
    width: 20px;
    height: 20px;
    color: #555;
}

.chat-messages {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 12px;
    background: #f8f9fa;
}

.chat-empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-style: italic;
}

.message {
    display: flex;
    max-width: 70%;
    margin-bottom: 8px;
}

.message.own {
    align-self: flex-end;
    justify-content: flex-end;
}

.message.other {
    align-self: flex-start;
    justify-content: flex-start;
}

.message-bubble {
    padding: 12px 16px;
    border-radius: 18px;
    position: relative;
    word-wrap: break-word;
    max-width: 100%;
}

.message.own .message-bubble {
    background: #4e9af1;
    color: white;
    border-bottom-right-radius: 6px;
}

.message.other .message-bubble {
    background: white;
    color: #333;
    border-bottom-left-radius: 6px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.message-time {
    font-size: 11px;
    opacity: 0.7;
    margin-top: 4px;
    display: block;
}

.chat-input-container {
    background: white;
    border-top: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    gap: 12px;
    align-items: flex-end;
}

.chat-input {
    flex: 1;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    font-size: 14px;
    resize: none;
    outline: none;
    transition: var(--transition);
    font-family: inherit;
    min-height: 20px;
    max-height: 100px;
}

.chat-input:focus {
    border-color: #4e9af1;
}

.send-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
}

.send-btn:hover {
    background: #3a7bc8;
}

.send-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.send-btn svg {
    width: 20px;
    height: 20px;
}

/* Profile View */
#profile-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.profile-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.profile-header h2 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.save-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
}

.save-btn:hover {
    background: #3a7bc8;
}

.save-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.profile-content {
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
}

.profile-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px;
}

.profile-section {
    background: white;
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.section-title {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin-bottom: 16px;
}

.section-subtitle {
    font-size: 14px;
    color: #666;
    margin-bottom: 16px;
}

/* Photo Grid */
.photo-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.photo-slot {
    aspect-ratio: 1;
    border: 2px dashed #ddd;
    border-radius: 12px;
    cursor: pointer;
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.photo-slot:hover {
    border-color: #4e9af1;
}

.photo-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #999;
    font-size: 12px;
}

.photo-placeholder svg {
    width: 24px;
    height: 24px;
    margin-bottom: 8px;
}

.photo-preview {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Form Elements */
.form-group {
    margin-bottom: 16px;
}

.form-group label {
    display: block;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    font-size: 14px;
}

.form-input {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: var(--transition);
    font-family: inherit;
}

.form-input:focus {
    outline: none;
    border-color: #4e9af1;
}

.form-input select {
    cursor: pointer;
}

/* Passions */
.passions-container {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 12px;
}

.passion-chip {
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 14px;
    background: white;
    color: #333;
}

.passion-chip:hover {
    border-color: #4e9af1;
}

.passion-chip.selected {
    background: #4e9af1;
    color: white;
    border-color: #4e9af1;
}

.passion-status {
    font-size: 12px;
    color: #666;
}

.passion-status.warning {
    color: #ff6b6b;
}

.passion-status.success {
    color: #4e9af1;
}

/* Settings View */
#settings-view {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.settings-header {
    background: white;
    border-bottom: 1px solid #eaeaea;
    padding: 12px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
    box-shadow: var(--shadow-sm);
    position: relative;
    z-index: 10;
}

.settings-header h2 {
    font-size: 20px;
    font-weight: 700;
    color: #333;
}

.settings-content {
    flex: 1;
    overflow-y: auto;
    background: #f8f9fa;
}

.settings-section {
    background: white;
    margin: 20px;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.settings-section:first-child {
    margin-top: 20px;
}

.settings-section:last-child {
    margin-bottom: 20px;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px 0;
    border-bottom: 1px solid #f0f0f0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.setting-label {
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.setting-value {
    font-weight: 600;
    color: #4e9af1;
    font-size: 14px;
}

.setting-status {
    font-size: 12px;
    color: #666;
}

.setting-action-btn {
    background: #4e9af1;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.setting-action-btn:hover {
    background: #3a7bc8;
}

.setting-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

/* Sliders */
.slider-container {
    margin: 16px 0;
}

.slider {
    width: 100%;
    height: 4px;
    border-radius: 2px;
    background: #ddd;
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #4e9af1;
    cursor: pointer;
    border: none;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.slider-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
    font-size: 12px;
    color: #666;
}

/* Toggle Switch */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 24px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
}

input:checked + .toggle-slider {
    background-color: #4e9af1;
}

input:checked + .toggle-slider:before {
    transform: translateX(26px);
}

/* Blocked Users */
.blocked-users-list {
    margin-top: 12px;
}

.no-blocked-users {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 20px;
}

.blocked-user-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 12px;
    border: 1px solid #f0f0f0;
    border-radius: 8px;
    margin-bottom: 8px;
}

.blocked-user-info {
    font-weight: 600;
    color: #333;
}

.unblock-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.unblock-btn:hover {
    background: #ff5252;
}

/* Logout Button */
.logout-btn {
    background: #ff6b6b;
    color: white;
    border: none;
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    gap: 8px;
    width: 100%;
    justify-content: center;
}

.logout-btn:hover {
    background: #ff5252;
}

.logout-btn svg {
    width: 18px;
    height: 18px;
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    opacity: 1;
    visibility: visible;
}

.modal-close-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.modal-close-btn svg {
    width: 24px;
    height: 24px;
    color: #333;
}

/* Profile Modal */
.profile-modal-content {
    background: white;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .profile-modal-content {
    transform: scale(1);
}

.profile-modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
    background: white;
}

.profile-modal-header h2 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.profile-options-btn {
    background: none;
    border: none;
    padding: 8px;
    border-radius: 50%;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-options-btn:hover {
    background: rgba(0, 0, 0, 0.1);
}

.profile-options-btn svg {
    width: 20px;
    height: 20px;
    color: #333;
}

.profile-modal-body {
    overflow-y: auto;
    max-height: calc(90vh - 80px);
}

/* Profile Photo Section */
.profile-photo-section {
    position: relative;
    background: #f8f9fa;
}

.profile-photo-container {
    aspect-ratio: 1;
    position: relative;
    overflow: hidden;
}

.profile-photo-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background: #f0f0f0;
}

.profile-avatar-large {
    width: 120px;
    height: 120px;
    border-radius: 60px;
    background: #4e9af1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 48px;
    font-weight: 700;
    color: white;
}

.profile-photo {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Photo Navigation */
.photo-nav {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
}

.photo-nav-btn {
    background: rgba(0, 0, 0, 0.5);
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    color: white;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
}

.photo-nav-btn:hover {
    background: rgba(0, 0, 0, 0.7);
}

.photo-nav-btn svg {
    width: 20px;
    height: 20px;
}

.photo-indicators {
    display: flex;
    gap: 8px;
}

.photo-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: var(--transition);
}

.photo-indicator.active {
    background: white;
}

/* Profile Info Section */
.profile-info-section {
    padding: 20px;
}

.profile-name-row {
    margin-bottom: 16px;
}

.profile-name-row h3 {
    font-size: 24px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.profile-description-section {
    margin-bottom: 20px;
}

.profile-description-section p {
    font-size: 16px;
    line-height: 1.5;
    color: #666;
    margin: 0;
}

.profile-passions-section h4 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 0 0 12px 0;
}

.profile-passion-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
}

.profile-passion-tag {
    background: #4e9af1;
    color: white;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 14px;
    font-weight: 500;
}

/* Chat Options Modal */
.chat-options-content {
    background: white;
    border-radius: 16px;
    width: 280px;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal-overlay.active .chat-options-content {
    transform: scale(1);
}

.chat-options-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px 20px;
    border-bottom: 1px solid #eaeaea;
}

.chat-options-header h3 {
    font-size: 18px;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.chat-options-list {
    padding: 8px 0;
}

.chat-option-item {
    width: 100%;
    background: none;
    border: none;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 16px;
    color: #333;
}

.chat-option-item:hover {
    background: #f8f9fa;
}

.chat-option-item.danger {
    color: #ff6b6b;
}

.chat-option-item.danger:hover {
    background: #fff5f5;
}

.chat-option-item svg {
    width: 20px;
    height: 20px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .app-header {
        padding: 1rem;
    }

    .header-content {
        padding: 0;
    }

    #home-view {
        padding: 1rem;
    }

    .shake-button {
        width: 150px;
        height: 150px;
    }

    .matches-list {
        grid-template-columns: 1fr;
    }

    .chat-header,
    .profile-header,
    .settings-header {
        padding: 1rem;
    }

    .chat-messages,
    .chat-input-container {
        padding: 1rem;
    }

    .profile-content,
    .settings-content {
        padding: 1rem;
    }
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(100%); }
    to { transform: translateX(0); }
}

.match-card {
    animation: fadeIn 0.5s ease-out;
}

.message {
    animation: fadeIn 0.3s ease-out;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.shake-button.shaking {
    animation: shake 0.5s ease-in-out;
}
