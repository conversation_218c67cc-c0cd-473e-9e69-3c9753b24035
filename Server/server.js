// server.js - Main server file with enhanced match handling
const express = require('express');
const http = require('http');
const path = require('path');
const { Server } = require('socket.io');
const mongoose = require('mongoose');
const bcrypt = require('bcrypt');
const cors = require('cors');
const cookieParser = require('cookie-parser');
const helmet = require('helmet');
const { v4: uuidv4 } = require('uuid');
const geolib = require('geolib');

// Import models
const User = require('./models/User');
const Match = require('./models/Match');
const Message = require('./models/Message');

const Report = require('./models/Report');
// Import middleware
const {
  generateToken,
  authenticate,
  requireAdmin,
  authenticateAdmin,
  validateInput,
  validationRules,
  auditLog,
  rateLimiters
} = require('./middleware/auth');

// Initialize Express app with increased header size limit
const app = express();
const server = http.createServer(app, {
  maxHeaderSize: 16384 // Increase max header size to 16KB (default is 8KB)
});
const io = new Server(server, {
  cors: {
    origin: '*', // In production, restrict this to your app domains
    methods: ['GET', 'POST']
  }
});

// Middleware
app.use(helmet({
  contentSecurityPolicy: false, // Disable CSP for admin dashboard
  crossOriginEmbedderPolicy: false
}));
app.use(cors({
  origin: true,
  credentials: true
}));
app.use(cookieParser());
// Increase JSON body size limit to 10MB
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ limit: '10mb', extended: true }));

// Debug middleware to log large requests/responses (only for admin routes)
app.use('/api/admin', (req, res, next) => {
  const headerSize = JSON.stringify(req.headers).length;
  if (headerSize > 4096) { // Log if headers are larger than 4KB
    console.warn(`Large request headers detected: ${headerSize} bytes for ${req.path}`);
  }
  next();
});

// Main web app route (desktop version)
app.get('/', (req, res) => {
  console.log('Serving main app from:', path.join(__dirname, 'web', 'app.html'));
  res.sendFile(path.join(__dirname, 'web', 'app.html'));
});

// Serve static files for main app
app.use('/assets', (req, res, next) => {
  console.log('Assets request:', req.url);
  next();
}, express.static(path.join(__dirname, 'web/assets')));

app.use('/css', (req, res, next) => {
  console.log('CSS request:', req.url);
  next();
}, express.static(path.join(__dirname, 'web/css')));

app.use('/js', (req, res, next) => {
  console.log('JS request:', req.url);
  next();
}, express.static(path.join(__dirname, 'web/js')));

// Admin login page route
app.get('/admin/login', (req, res) => {
  res.sendFile(path.join(__dirname, 'web', 'login.html'));
});

// Protected admin dashboard route
app.get('/admin', async (req, res) => {
  try {
    // Check authentication
    const token = req.cookies?.adminToken;
    if (!token) {
      return res.redirect('/admin/login');
    }

    const { verifyToken } = require('./middleware/auth');
    const decoded = verifyToken(token);
    if (!decoded) {
      return res.redirect('/admin/login');
    }

    // Check if user exists and is admin
    const user = await User.findById(decoded.userId);
    if (!user || user.role !== 'admin' || user.accountStatus !== 'active') {
      return res.redirect('/admin/login');
    }

    res.sendFile(path.join(__dirname, 'web', 'index.html'));
  } catch (error) {
    console.error('Admin dashboard access error:', error);
    res.redirect('/admin/login');
  }
});

// Serve static files from web directory (for admin assets)
app.use('/admin/assets', express.static('web'));

// Add favicon endpoint to prevent 404 errors
app.get('/favicon.ico', (req, res) => {
  res.status(204).end(); // No content response
});

// Database connection with enhanced configuration
const mongoOptions = {
  serverSelectionTimeoutMS: 30000, // 30 seconds timeout for server selection
  socketTimeoutMS: 45000, // 45 seconds socket timeout
  connectTimeoutMS: 30000, // 30 seconds connection timeout
  maxPoolSize: 10, // Maintain up to 10 socket connections
  minPoolSize: 5, // Maintain a minimum of 5 socket connections
  maxIdleTimeMS: 30000, // Close connections after 30 seconds of inactivity
  retryWrites: true, // Retry failed writes
  retryReads: true, // Retry failed reads
};

// MongoDB connection string - WITH AUTHENTICATION
const mongoURI = process.env.MONGODB_URI || 'mongodb://shakeapp:ShakeApp123!@127.0.0.1:27017/shakeAndMatch';

console.log('Attempting to connect to MongoDB at:', mongoURI);

mongoose.connect(mongoURI, mongoOptions)
.then(() => {
  console.log('Connected to MongoDB successfully');
  console.log('MongoDB connection state:', mongoose.connection.readyState);
})
.catch(err => {
  console.error('MongoDB connection error:', err);
  console.error('Connection failed. Please check:');
  console.error('1. MongoDB service is running');
  console.error('2. MongoDB is configured to accept connections');
  console.error('3. Firewall allows port 27017');
  console.error('4. Network connectivity to the database server');
});

// MongoDB connection status monitoring
mongoose.connection.on('connected', () => {
  console.log('MongoDB connected successfully');
});

mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB disconnected');
});

// Active users and their socket connections
const activeUsers = new Map(); // userId -> { socket, location, maxDistance, lastShake }

// Track match restrictions for users
const matchRestrictions = new Map(); // Map to store user pairs that can't be matched

// Debug function to log active users
const logActiveUsers = () => {
  console.log(`Active users count: ${activeUsers.size}`);
  activeUsers.forEach((user, userId) => {
    console.log(`User ${user.username} (${userId}): lastShake=${user.lastShake ? 'yes' : 'no'}, location=${user.location ? 'set' : 'not set'}`);
  });
};

// ===== REST API Routes =====

// Ping endpoint to check server connectivity
app.get('/api/ping', (req, res) => {
  res.status(200).json({ message: 'Server is running' });
});

// User registration
app.post('/api/register', async (req, res) => {
  try {
    const { username, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ username });
    if (existingUser) {
      return res.status(400).json({ message: 'Username already taken' });
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = new User({
      username,
      password: hashedPassword,
    });

    await newUser.save();

    res.status(201).json({ message: 'User registered successfully' });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({ message: 'Server error' });
  }
});

// Social login endpoint
app.post('/api/social-login', async (req, res) => {
  // Set CORS headers
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    console.log('Social login request received:', req.body);
    const { provider, providerId, email, name, photo, token } = req.body;

    if (!provider || !providerId) {
      console.error('Missing provider or providerId in request');
      return res.status(400).json({ message: 'Provider and provider ID are required' });
    }

    console.log(`Social login attempt for provider: ${provider}, ID: ${providerId}`);

    // Check MongoDB connection status
    if (mongoose.connection.readyState !== 1) {
      console.error('MongoDB not connected. Connection state:', mongoose.connection.readyState);
      return res.status(503).json({
        message: 'Database connection unavailable',
        error: 'MongoDB connection is not ready'
      });
    }

    // Look for existing user with this social provider ID
    let user = await User.findOne({
      $or: [
        { [`socialAuth.${provider}.id`]: providerId },
        { email: email } // Also check by email if provided
      ]
    }).maxTimeMS(10000);

    if (user) {
      // User exists, update social auth info if needed
      if (!user.socialAuth) {
        user.socialAuth = {};
      }
      if (!user.socialAuth[provider]) {
        user.socialAuth[provider] = {
          id: providerId,
          email: email,
          name: name,
          photo: photo
        };
        await user.save();
      }

      console.log(`Existing user logged in via ${provider}: ${user.username}, ID: ${user._id}`);
    } else {
      // Create new user from social login
      const username = name || email?.split('@')[0] || `${provider}_user_${Date.now()}`;

      user = new User({
        username,
        password: await bcrypt.hash('social_login_no_password', 10), // Placeholder password
        email: email,
        socialAuth: {
          [provider]: {
            id: providerId,
            email: email,
            name: name,
            photo: photo
          }
        }
      });

      await user.save();
      console.log(`New user created via ${provider}: ${username}, ID: ${user._id}`);
    }

    // Convert the MongoDB _id to string format
    const userId = user._id.toString();

    // Generate user token (avoid var name collision with provider token)
    const authToken = generateToken(user);

    // Prepare response data
    const responseData = {
      message: 'Social login successful',
      userId: userId,
      username: user.username,
      token: authToken,
      user: {
        id: userId,
        username: user.username,
        email: user.email,
        createdAt: user.createdAt,
      }
    };

    console.log('Sending social login response:', JSON.stringify(responseData));

    // Set content type explicitly
    res.header('Content-Type', 'application/json');

    // Return user data
    res.status(200).json(responseData);
  } catch (error) {
    console.error('Social login error:', error);
    res.status(500).json({
      message: 'Social login failed',
      error: error.message
    });
  }
});

// User login with explicit headers and CORS support
app.post('/api/login', async (req, res) => {
  // Set CORS headers for this specific endpoint
  res.header('Access-Control-Allow-Origin', '*');
  res.header('Access-Control-Allow-Methods', 'POST, OPTIONS');
  res.header('Access-Control-Allow-Headers', 'Origin, X-Requested-With, Content-Type, Accept');

  // Handle preflight OPTIONS request
  if (req.method === 'OPTIONS') {
    return res.status(200).end();
  }

  try {
    console.log('Login request received:', req.body);
    const { username, password } = req.body;

    if (!username || !password) {
      console.error('Missing username or password in request');
      return res.status(400).json({ message: 'Username and password are required' });
    }

    console.log(`Login attempt for username: ${username}`);

    // Check MongoDB connection status before attempting query
    if (mongoose.connection.readyState !== 1) {
      console.error('MongoDB not connected. Connection state:', mongoose.connection.readyState);
      return res.status(503).json({
        message: 'Database connection unavailable',
        error: 'MongoDB connection is not ready'
      });
    }

    // Find user with timeout handling
    const user = await User.findOne({ username }).maxTimeMS(10000);
    if (!user) {
      console.log(`Login failed: User not found - ${username}`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Check password
    const isPasswordValid = await bcrypt.compare(password, user.password);
    if (!isPasswordValid) {
      console.log(`Login failed: Invalid password for ${username}`);
      return res.status(401).json({ message: 'Invalid credentials' });
    }

    // Explicitly convert the MongoDB _id to string format
    const userId = user._id.toString();
    console.log(`User logged in: ${username}, ID: ${userId}`);

    // Generate user token
    const token = generateToken(user);

    // Log the exact data being sent back
    const responseData = {
      message: 'Login successful',
      userId: userId,
      token,
      user: {
        id: userId,
        username: user.username,
        createdAt: user.createdAt,
      }
    };

    console.log('Sending login response:', JSON.stringify(responseData));

    // Set content type explicitly
    res.header('Content-Type', 'application/json');

    // Return user data with consistent property naming
    res.status(200).json(responseData);
  } catch (error) {
    console.error('Login error:', error);

    // Provide specific error messages for different types of MongoDB errors
    if (error.name === 'MongooseError' && error.message.includes('buffering timed out')) {
      console.error('MongoDB connection timeout - database may be unreachable');
      return res.status(503).json({
        message: 'Database connection timeout',
        error: 'Unable to connect to database. Please try again later.'
      });
    } else if (error.name === 'MongoNetworkError') {
      console.error('MongoDB network error - connection failed');
      return res.status(503).json({
        message: 'Database network error',
        error: 'Database server is unreachable'
      });
    } else if (error.name === 'MongoTimeoutError') {
      console.error('MongoDB timeout error');
      return res.status(503).json({
        message: 'Database timeout',
        error: 'Database operation timed out'
      });
    }

    // Generic server error for other cases
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Token validation endpoint for desktop app
app.post('/api/validate-token', async (req, res) => {
  try {
    const token = req.headers.authorization?.replace('Bearer ', '');

    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    const { verifyToken } = require('./middleware/auth');
    const decoded = verifyToken(token);

    if (!decoded) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    const user = await User.findById(decoded.userId);
    if (!user || user.accountStatus !== 'active') {
      return res.status(401).json({ error: 'User not found or inactive' });
    }

    // Get user profile
    const profile = {
      age: user.age,
      description: user.description,
      passions: user.passions,
      images: user.images
    };

    res.json({
      user: {
        id: user._id,
        username: user.username,
        email: user.email
      },
      profile
    });
  } catch (error) {
    console.error('Token validation error:', error);
    res.status(500).json({ error: 'Server error' });
  }
});

// Get user matches endpoint
app.get('/api/matches', authenticate, async (req, res) => {
  try {
    const userId = req.user._id;

    const matches = await Match.find({
      $or: [
        { user1: userId },
        { user2: userId }
      ]
    }).populate('user1 user2', 'username age images');

    const formattedMatches = matches.map(match => {
      const otherUser = match.user1._id.toString() === userId.toString() ? match.user2 : match.user1;
      return {
        userId: otherUser._id,
        username: otherUser.username,
        age: otherUser.age,
        profileImage: otherUser.images && otherUser.images.length > 0 ? otherUser.images[0] : null,
        distance: match.distance || 0,
        createdAt: match.createdAt
      };
    });

    res.json({ matches: formattedMatches });
  } catch (error) {
    console.error('Error fetching matches:', error);
    res.status(500).json({ error: 'Failed to fetch matches' });
  }
});

// Get messages with a specific user
app.get('/api/messages/:userId', authenticate, async (req, res) => {
  try {
    const currentUserId = req.user._id;
    const otherUserId = req.params.userId;

    const messages = await Message.find({
      $or: [
        { senderId: currentUserId, recipientId: otherUserId },
        { senderId: otherUserId, recipientId: currentUserId }
      ]
    }).sort({ timestamp: 1 });

    res.json({ messages });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

// Shake endpoint for desktop app
app.post('/api/shake', authenticate, async (req, res) => {
  try {
    const { latitude, longitude } = req.body;
    const userId = req.user._id;

    if (!latitude || !longitude) {
      return res.status(400).json({ error: 'Location coordinates required' });
    }

    // Update user's location
    await User.findByIdAndUpdate(userId, {
      location: {
        type: 'Point',
        coordinates: [longitude, latitude]
      },
      lastActive: new Date()
    });

    // Find nearby users (simplified version)
    const nearbyUsers = await User.find({
      _id: { $ne: userId },
      location: {
        $near: {
          $geometry: {
            type: 'Point',
            coordinates: [longitude, latitude]
          },
          $maxDistance: 10000 // 10km radius
        }
      },
      accountStatus: 'active'
    }).limit(10);

    if (nearbyUsers.length === 0) {
      return res.json({ match: null, message: 'No users found nearby' });
    }

    // Randomly select a user for matching (simplified matching logic)
    const randomUser = nearbyUsers[Math.floor(Math.random() * nearbyUsers.length)];

    // Check if match already exists
    const existingMatch = await Match.findOne({
      $or: [
        { user1: userId, user2: randomUser._id },
        { user1: randomUser._id, user2: userId }
      ]
    });

    if (existingMatch) {
      return res.json({ match: null, message: 'Already matched with nearby users' });
    }

    // Create new match
    const newMatch = new Match({
      user1: userId,
      user2: randomUser._id,
      distance: 1.0 // Calculate actual distance if needed
    });

    await newMatch.save();

    // Return match data
    const matchData = {
      userId: randomUser._id,
      username: randomUser.username,
      age: randomUser.age,
      profileImage: randomUser.images && randomUser.images.length > 0 ? randomUser.images[0] : null,
      distance: 1.0
    };

    res.json({ match: matchData });
  } catch (error) {
    console.error('Shake error:', error);
    res.status(500).json({ error: 'Shake failed' });
  }
});

// ===== ADMIN AUTHENTICATION ROUTES =====

// Admin login endpoint
app.post('/api/admin/login',
  rateLimiters.auth,
  validateInput([
    validationRules.username,
    validationRules.password
  ]),
  auditLog('admin_login_attempt'),
  async (req, res) => {
    try {
      const { username, password } = req.body;

      console.log(`Admin login attempt for username: ${username}`);

      // Find user
      const user = await User.findOne({ username }).select('+password');
      if (!user) {
        console.log(`Admin login failed: User not found - ${username}`);
        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Check if user is admin
      if (user.role !== 'admin') {
        console.log(`Admin login failed: User is not admin - ${username}`);
        return res.status(403).json({ error: 'Admin access required' });
      }

      // Check account status
      if (user.accountStatus !== 'active') {
        console.log(`Admin login failed: Account not active - ${username}`);
        return res.status(401).json({ error: 'Account is not active' });
      }

      // Check if account is locked
      if (user.isLocked) {
        console.log(`Admin login failed: Account locked - ${username}`);
        return res.status(401).json({ error: 'Account is temporarily locked due to too many failed attempts' });
      }

      // Verify password
      const isPasswordValid = await bcrypt.compare(password, user.password);
      if (!isPasswordValid) {
        console.log(`Admin login failed: Invalid password for ${username}`);

        // Increment login attempts
        await user.incLoginAttempts();

        return res.status(401).json({ error: 'Invalid credentials' });
      }

      // Reset login attempts and update last login
      await user.resetLoginAttempts();

      // Generate JWT token
      const token = generateToken(user);

      // Set HTTP-only cookie
      res.cookie('adminToken', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 24 * 60 * 60 * 1000 // 24 hours
      });

      console.log(`Admin logged in successfully: ${username}`);

      res.status(200).json({
        message: 'Login successful',
        user: {
          id: user._id.toString(),
          username: user.username,
          role: user.role,
          email: user.email
        }
      });

    } catch (error) {
      console.error('Admin login error:', error);
      res.status(500).json({ error: 'Login failed' });
    }
  }
);

// Admin logout endpoint
app.post('/api/admin/logout',
  authenticate,
  auditLog('admin_logout'),
  (req, res) => {
    try {
      // Clear the cookie
      res.clearCookie('adminToken');

      console.log(`Admin logged out: ${req.user.username}`);

      res.status(200).json({ message: 'Logout successful' });
    } catch (error) {
      console.error('Admin logout error:', error);
      res.status(500).json({ error: 'Logout failed' });
    }
  }
);

// Check admin authentication status
app.get('/api/admin/auth/status',
  authenticate,
  (req, res) => {
    try {
      res.status(200).json({
        authenticated: true,
        user: {
          id: req.user._id.toString(),
          username: req.user.username,
          role: req.user.role,
          email: req.user.email
        }
      });
    } catch (error) {
      console.error('Auth status check error:', error);
      res.status(500).json({ error: 'Status check failed' });
    }
  }
);

// ===== PROFILE API ROUTES =====

// Get user profile
app.get('/api/profile/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`Fetching profile for user ID: ${userId}`);

    // Validate userId format if using MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.error(`Invalid user ID format: ${userId}`);
      return res.status(400).json({ message: 'Invalid user ID format' });
    }

    // Find user
    let user = await User.findById(userId);

    // If user doesn't exist, create an empty profile
    if (!user) {
      console.log(`User not found with ID: ${userId}, creating empty profile`);

      // Create placeholder username
      const placeholderUsername = `User_${userId.substring(0, 6)}`;

      user = new User({
        _id: userId,
        username: placeholderUsername,
        password: await bcrypt.hash('defaultpassword', 10),
        createdAt: new Date()
      });

      try {
        await user.save();
        console.log(`Created empty profile for user ID: ${userId}`);
      } catch (createError) {
        console.error('Error creating empty profile:', createError);
        return res.status(500).json({
          message: 'Error creating user profile',
          error: createError.message
        });
      }
    }

    // Return profile data
    const profileData = {
      username: user.username,
      age: user.age || null,
      description: user.description || '',
      passions: user.passions || [],
      images: user.images || [],
      createdAt: user.createdAt
    };

    res.status(200).json({ profile: profileData });
  } catch (error) {
    console.error('Error fetching profile:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Update user profile
app.put('/api/profile/:userId', async (req, res) => {
  try {
    const { userId } = req.params;
    const { age, description, passions, images } = req.body;

    console.log(`Updating profile for user ID: ${userId}`);

    // Validate userId format if using MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.error(`Invalid user ID format: ${userId}`);
      return res.status(400).json({ message: 'Invalid user ID format' });
    }

    // Find user with more detailed logging
    let user = await User.findById(userId);

    // If user doesn't exist in the database yet, create a new user profile
    if (!user) {
      console.log(`User not found with ID: ${userId}, creating new profile`);

      // Try to create a minimal user object - we need to get username from somewhere
      // For now, use "User" + truncated ID as a placeholder username
      const placeholderUsername = `User_${userId.substring(0, 6)}`;

      // Create a new user with minimal required fields
      user = new User({
        _id: userId, // Use the provided ID
        username: placeholderUsername,
        password: await bcrypt.hash('defaultpassword', 10), // This should be updated later
        createdAt: new Date()
      });

      try {
        await user.save();
        console.log(`Created new user profile with ID: ${userId}`);
      } catch (createError) {
        console.error('Error creating user profile:', createError);
        return res.status(500).json({
          message: 'Error creating user profile',
          error: createError.message
        });
      }
    } else {
      console.log(`Found user: ${user.username}`);
    }

    // Validate image count
    if (images && images.length > 4) {
      return res.status(400).json({ message: 'Maximum 4 images allowed' });
    }

    // Update fields if provided
    if (age !== undefined) user.age = age;
    if (description !== undefined) user.description = description;
    if (passions !== undefined) user.passions = passions;
    if (images !== undefined) user.images = images;

    // Save with error handling
    try {
      await user.save();
      console.log(`Profile updated successfully for user: ${user.username}`);
    } catch (saveError) {
      console.error('Error saving user:', saveError);
      return res.status(500).json({
        message: 'Error saving profile',
        error: saveError.message
      });
    }

    // Return updated profile data
    const updatedProfile = {
      username: user.username,
      age: user.age,
      description: user.description,
      passions: user.passions,
      images: user.images,
      createdAt: user.createdAt
    };

    res.status(200).json({ message: 'Profile updated successfully', profile: updatedProfile });
  } catch (error) {
    console.error('Error updating profile:', error);

    // Handle validation errors
    if (error.name === 'ValidationError') {
      return res.status(400).json({ message: error.message });
    }

    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Debug endpoint to check if a user exists
app.get('/api/debug/user/:userId', async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`Debug: Checking if user exists with ID: ${userId}`);

    // Check if ID is valid MongoDB ObjectId
    const isValidObjectId = mongoose.Types.ObjectId.isValid(userId);

    if (!isValidObjectId) {
      return res.status(400).json({
        message: 'Invalid user ID format',
        valid: false,
        format: 'invalid',
        id: userId
      });
    }

    // Try to find user
    const user = await User.findById(userId);

    if (user) {
      // User exists
      return res.status(200).json({
        message: 'User found',
        exists: true,
        username: user.username,
        id: userId,
        createdAt: user.createdAt
      });
    } else {
      // User does not exist
      return res.status(404).json({
        message: 'User not found',
        exists: false,
        id: userId
      });
    }
  } catch (error) {
    console.error('Error in debug endpoint:', error);
    res.status(500).json({
      message: 'Server error',
      error: error.message
    });
  }
});

// Debug endpoint to get all users with pagination (be careful with this in production!)
app.get('/api/debug/users', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 100, 500); // Max 500 per page
    const skip = (page - 1) * limit;

    // Get users with pagination, but only return non-sensitive fields and exclude large image data
    const [users, totalCount] = await Promise.all([
      User.find({}, 'username _id createdAt age description passions')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      User.countDocuments()
    ]);

    res.status(200).json({
      count: users.length,
      totalCount: totalCount,
      page: page,
      totalPages: Math.ceil(totalCount / limit),
      users: users.map(user => ({
        id: user._id,
        username: user.username,
        createdAt: user.createdAt,
        age: user.age,
        description: user.description,
        passions: user.passions,
        imageCount: 0 // We'll get this separately if needed
      }))
    });
  } catch (error) {
    console.error('Error fetching all users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Debug endpoint to list all active users
app.get('/api/debug/active-users', (req, res) => {
  try {
    const activeUsersList = [];

    activeUsers.forEach((userData, userId) => {
      activeUsersList.push({
        userId,
        username: userData.username,
        socketConnected: !!userData.socket,
        hasLocation: !!userData.location,
        lastShake: userData.lastShake || null
      });
    });

    res.status(200).json({
      count: activeUsersList.length,
      users: activeUsersList
    });
  } catch (error) {
    console.error('Error listing active users:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin endpoint to get all matches with pagination
app.get('/api/admin/matches',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 100, 500); // Max 500 per page
    const skip = (page - 1) * limit;

    const [matches, totalCount] = await Promise.all([
      Match.find({})
        .populate('user1', 'username')
        .populate('user2', 'username')
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit),
      Match.countDocuments()
    ]);

    const formattedMatches = matches.map(match => ({
      id: match._id.toString(),
      user1: match.user1._id.toString(),
      user1Username: match.user1.username,
      user2: match.user2._id.toString(),
      user2Username: match.user2.username,
      distance: match.distance,
      location1: match.location1,
      location2: match.location2,
      createdAt: match.createdAt
    }));

    res.status(200).json({
      count: formattedMatches.length,
      totalCount: totalCount,
      page: page,
      totalPages: Math.ceil(totalCount / limit),
      matches: formattedMatches
    });
  } catch (error) {
    console.error('Error fetching matches:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin endpoint to get all messages with pagination
app.get('/api/admin/messages',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = Math.min(parseInt(req.query.limit) || 100, 500); // Max 500 per page
    const skip = (page - 1) * limit;

    const [messages, totalCount] = await Promise.all([
      Message.find({})
        .sort({ timestamp: -1 })
        .skip(skip)
        .limit(limit),
      Message.countDocuments()
    ]);

    res.status(200).json({
      count: messages.length,
      totalCount: totalCount,
      page: page,
      totalPages: Math.ceil(totalCount / limit),
      messages: messages
    });
  } catch (error) {
    console.error('Error fetching messages:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin endpoint to get messages between two specific users
app.get('/api/admin/messages/:userId1/:userId2',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
  try {
    const { userId1, userId2 } = req.params;

    // Validate user IDs
    if (!mongoose.Types.ObjectId.isValid(userId1) || !mongoose.Types.ObjectId.isValid(userId2)) {
      return res.status(400).json({ error: 'Invalid user ID format' });
    }

    // Get user information
    const [user1, user2] = await Promise.all([
      User.findById(userId1).select('username'),
      User.findById(userId2).select('username')
    ]);

    if (!user1 || !user2) {
      return res.status(404).json({ error: 'One or both users not found' });
    }

    // Find all messages between these two users
    const messages = await Message.find({
      $or: [
        { senderId: userId1, receiverId: userId2 },
        { senderId: userId2, receiverId: userId1 }
      ]
    })
    .sort({ timestamp: 1 }) // Sort chronologically
    .lean();

    // Format messages for the admin interface
    const formattedMessages = messages.map(msg => ({
      id: msg.messageId,
      text: msg.text,
      senderId: msg.senderId.toString(),
      senderUsername: msg.senderUsername,
      receiverId: msg.receiverId.toString(),
      receiverUsername: msg.receiverUsername,
      timestamp: msg.timestamp,
      read: msg.read
    }));

    res.status(200).json({
      user1: { id: userId1, username: user1.username },
      user2: { id: userId2, username: user2.username },
      messages: formattedMessages,
      totalCount: formattedMessages.length
    });

  } catch (error) {
    console.error('Error fetching messages between users:', error);
    res.status(500).json({ error: 'Failed to fetch messages' });
  }
});

// Admin endpoint to get dashboard statistics
app.get('/api/admin/stats',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
  try {
    const [userCount, matchCount, messageCount] = await Promise.all([
      User.countDocuments(),
      Match.countDocuments(),
      Message.countDocuments()
    ]);

    const activeUserCount = activeUsers.size;

    // Get recent activity (last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const [recentMatches, recentMessages, recentUsers] = await Promise.all([
      Match.countDocuments({ createdAt: { $gte: yesterday } }),
      Message.countDocuments({ timestamp: { $gte: yesterday } }),
      User.countDocuments({ createdAt: { $gte: yesterday } })
    ]);

    res.status(200).json({
      totalUsers: userCount,
      activeUsers: activeUserCount,
      totalMatches: matchCount,
      totalMessages: messageCount,
      recentActivity: {
        matches: recentMatches,
        messages: recentMessages,
        newUsers: recentUsers
      }
    });
  } catch (error) {
    console.error('Error fetching stats:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
// User endpoint to report a user
app.post('/api/report', authenticate, async (req, res) => {
  try {
    const { reportedUserId, reason, details } = req.body || {};
    console.log('Report submission:', { reporterId: req.user._id, reportedUserId, reason, details });

    if (!reportedUserId || !mongoose.Types.ObjectId.isValid(reportedUserId)) {
      return res.status(400).json({ error: 'Invalid or missing reportedUserId' });
    }
    if (!reason || typeof reason !== 'string' || reason.length > 200) {
      return res.status(400).json({ error: 'Reason is required and must be <= 200 chars' });
    }
    if (req.user._id.toString() === reportedUserId) {
      return res.status(400).json({ error: 'You cannot report yourself' });
    }

    const reportedUser = await User.findById(reportedUserId).select('_id username accountStatus');
    if (!reportedUser) {
      return res.status(404).json({ error: 'Reported user not found' });
    }

    const report = await Report.create({
      reporterId: req.user._id,
      reportedUserId,
      reason: reason.trim(),
      details: (details || '').toString().slice(0, 2000)
    });

    console.log('Report created:', report._id);
    res.status(201).json({ message: 'Report submitted', reportId: report._id.toString() });
  } catch (error) {
    console.error('Error submitting report:', error);
    res.status(500).json({ error: 'Failed to submit report' });
  }
});

// Admin: list reports with pagination and status filter
app.get('/api/admin/reports', authenticateAdmin, async (req, res) => {
  try {
    console.log('=== ADMIN REPORTS REQUEST ===');
    console.log('Query params:', req.query);
    console.log('Admin user:', req.user?.username);

    // First, let's check if we have ANY reports at all
    const totalReportsInDB = await Report.countDocuments({});
    console.log('Total reports in database:', totalReportsInDB);

    if (totalReportsInDB === 0) {
      console.log('No reports found in database');
      return res.status(200).json({
        count: 0,
        totalCount: 0,
        page: 1,
        totalPages: 0,
        reports: []
      });
    }

    const page = Math.max(1, parseInt(req.query.page) || 1);
    const limit = Math.min(Math.max(1, parseInt(req.query.limit) || 50), 200);
    const skip = (page - 1) * limit;
    const status = (req.query.status || 'open').toLowerCase(); // open|resolved|all

    const filter = {};
    if (status === 'open') filter.status = 'open';
    else if (status === 'resolved') filter.status = 'resolved';

    console.log('Reports filter:', filter);

    const [reports, totalCount] = await Promise.all([
      Report.find(filter)
        .sort({ createdAt: -1 })
        .skip(skip)
        .limit(limit)
        .populate('reporterId', 'username')
        .populate('reportedUserId', 'username accountStatus warningCount')
        .lean(),
      Report.countDocuments(filter)
    ]);

    console.log(`Found ${reports.length} reports, total: ${totalCount}`);

    res.status(200).json({
      count: reports.length,
      totalCount,
      page,
      totalPages: Math.ceil(totalCount / limit),
      reports
    });
  } catch (error) {
    console.error('Error fetching reports:', error);
    res.status(500).json({ error: 'Failed to fetch reports' });
  }
});

// Admin: get single report
app.get('/api/admin/reports/:reportId', authenticateAdmin, async (req, res) => {
  try {
    const { reportId } = req.params;
    if (!mongoose.Types.ObjectId.isValid(reportId)) {
      return res.status(400).json({ error: 'Invalid report ID' });
    }

    const report = await Report.findById(reportId)
      .populate('reporterId', 'username email')
      .populate('reportedUserId', 'username accountStatus warningCount warnings')
      .lean();

    if (!report) return res.status(404).json({ error: 'Report not found' });

    res.status(200).json({ report });
  } catch (error) {
    console.error('Error fetching report:', error);
    res.status(500).json({ error: 'Failed to fetch report' });
  }
});

// Admin: resolve report (warning/block/ignore)
app.post('/api/admin/reports/:reportId/resolve', authenticateAdmin, async (req, res) => {
  try {
    const { reportId } = req.params;
    const { action, notes } = req.body || {};

    if (!mongoose.Types.ObjectId.isValid(reportId)) {
      return res.status(400).json({ error: 'Invalid report ID' });
    }
    const allowed = ['warning', 'block', 'ignore', 'other'];
    if (!allowed.includes(action)) {
      return res.status(400).json({ error: `Invalid action. Allowed: ${allowed.join(', ')}` });
    }

    const report = await Report.findById(reportId);
    if (!report) return res.status(404).json({ error: 'Report not found' });
    if (report.status === 'resolved') {
      return res.status(400).json({ error: 'Report already resolved' });
    }

    // Perform action on reported user
    let updatedUser = null;
    if (action === 'warning') {
      updatedUser = await User.findByIdAndUpdate(
        report.reportedUserId,
        {
          $push: { warnings: { reason: report.reason, notes: (notes || ''), issuedBy: req.user._id, issuedAt: new Date() } },
          $inc: { warningCount: 1 },
          $set: { updatedAt: new Date() }
        },
        { new: true }
      ).select('-password -loginAttempts -lockUntil');
    } else if (action === 'block') {
      updatedUser = await User.findByIdAndUpdate(
        report.reportedUserId,
        { $set: { accountStatus: 'blocked', updatedAt: new Date() } },
        { new: true }
      ).select('-password -loginAttempts -lockUntil');
      // Remove from active users map if present
      activeUsers.delete(report.reportedUserId.toString());
    }

    // Update report
    report.status = 'resolved';
    report.resolution = {
      action,
      notes: (notes || '').toString().slice(0, 2000),
      resolvedBy: req.user._id,
      resolvedAt: new Date()
    };
    await report.save();

    res.status(200).json({ message: 'Report resolved', report, user: updatedUser });
  } catch (error) {
    console.error('Error resolving report:', error);
    res.status(500).json({ error: 'Failed to resolve report' });
  }
});

// DEBUG: Create a test report (remove this after testing)
app.post('/api/admin/create-test-report', authenticateAdmin, async (req, res) => {
  try {
    // Find any two users to create a test report
    const users = await User.find({}).limit(2).select('_id username');
    if (users.length < 2) {
      return res.status(400).json({ error: 'Need at least 2 users to create test report' });
    }

    const testReport = await Report.create({
      reporterId: users[0]._id,
      reportedUserId: users[1]._id,
      reason: 'Test report - inappropriate content',
      details: 'This is a test report created by admin for debugging purposes',
      status: 'open'
    });

    console.log('Test report created:', testReport._id);
    res.status(201).json({ message: 'Test report created', reportId: testReport._id });
  } catch (error) {
    console.error('Error creating test report:', error);
    res.status(500).json({ error: 'Failed to create test report' });
  }
});

});

// Admin-specific profile endpoint that excludes large image data
app.get('/api/admin/profile/:userId',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
  try {
    const { userId } = req.params;

    console.log(`Fetching admin profile for user ID: ${userId}`);

    // Validate userId format if using MongoDB ObjectId
    if (!mongoose.Types.ObjectId.isValid(userId)) {
      console.error(`Invalid user ID format: ${userId}`);
      return res.status(400).json({ message: 'Invalid user ID format' });
    }

    // Find user but exclude large image data for admin view
    const user = await User.findById(userId).select('-password');

    if (!user) {
      console.log(`User not found: ${userId}`);
      return res.status(404).json({ message: 'User not found' });
    }

    // Get image count safely
    const imageCount = user.images ? user.images.length : 0;

    console.log(`Profile found for user: ${user.username}`);

    // Return profile data without large images, with safe defaults
    const profileData = {
      profile: {
        id: user._id.toString(),
        username: user.username || 'Unknown',
        email: user.email || null,
        age: user.age || null,
        description: user.description || '',
        passions: user.passions || [],
        imageCount: imageCount,
        createdAt: user.createdAt,
        socialAuth: user.socialAuth || null
      }
    };

    res.status(200).json(profileData);
  } catch (error) {
    console.error('Error fetching admin profile:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// Admin endpoint to delete a user
app.delete('/api/admin/users/:userId',
  authenticateAdmin,
  rateLimiters.sensitiveAction,
  auditLog('delete_user'),
  async (req, res) => {
  try {
    const { userId } = req.params;

    if (!mongoose.Types.ObjectId.isValid(userId)) {
      return res.status(400).json({ message: 'Invalid user ID format' });
    }

    // Remove user from active users if present
    activeUsers.delete(userId);

    // Delete user's messages
    await Message.deleteMany({
      $or: [
        { senderId: userId },
        { receiverId: userId }
      ]
    });

    // Delete user's matches
    await Match.deleteMany({
      $or: [
        { user1: userId },
        { user2: userId }
      ]
    });

    // Delete the user
    const deletedUser = await User.findByIdAndDelete(userId);

    if (!deletedUser) {
      return res.status(404).json({ message: 'User not found' });
    }

    console.log(`Admin deleted user: ${deletedUser.username} (${userId})`);

    res.status(200).json({
      message: 'User deleted successfully',
      deletedUser: {
        id: userId,
        username: deletedUser.username
      }
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    res.status(500).json({ message: 'Server error', error: error.message });
  }
});

// ===== ENHANCED USER MANAGEMENT API =====

// Get all users with advanced filtering and pagination
app.get('/api/admin/users',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
    try {
      const {
        page = 1,
        limit = 50,
        search = '',
        role = '',
        accountStatus = '',
        sortBy = 'createdAt',
        sortOrder = 'desc'
      } = req.query;

      const pageNum = Math.max(1, parseInt(page));
      const limitNum = Math.min(Math.max(1, parseInt(limit)), 100);
      const skip = (pageNum - 1) * limitNum;

      // Build filter query
      const filter = {};

      if (search) {
        filter.$or = [
          { username: { $regex: search, $options: 'i' } },
          { email: { $regex: search, $options: 'i' } }
        ];
      }

      if (role && ['user', 'admin'].includes(role)) {
        filter.role = role;
      }

      if (accountStatus && ['active', 'blocked', 'deleted'].includes(accountStatus)) {
        filter.accountStatus = accountStatus;
      }

      // Build sort query
      const sortQuery = {};
      const validSortFields = ['createdAt', 'updatedAt', 'lastLogin', 'username', 'email'];
      if (validSortFields.includes(sortBy)) {
        sortQuery[sortBy] = sortOrder === 'asc' ? 1 : -1;
      } else {
        sortQuery.createdAt = -1;
      }

      // Execute queries
      const [users, totalCount] = await Promise.all([
        User.find(filter)
          .select('-password -loginAttempts -lockUntil')
          .sort(sortQuery)
          .skip(skip)
          .limit(limitNum)
          .lean(),
        User.countDocuments(filter)
      ]);

      // Add computed fields
      const enhancedUsers = users.map(user => ({
        ...user,
        id: user._id.toString(),
        isOnline: user.lastLogin && (new Date() - new Date(user.lastLogin)) < 5 * 60 * 1000,
        imageCount: user.images ? user.images.length : 0
      }));

      res.status(200).json({
        users: enhancedUsers,
        pagination: {
          currentPage: pageNum,
          totalPages: Math.ceil(totalCount / limitNum),
          totalUsers: totalCount,
          usersPerPage: limitNum,
          hasNextPage: pageNum < Math.ceil(totalCount / limitNum),
          hasPrevPage: pageNum > 1
        },
        filters: {
          search,
          role,
          accountStatus,
          sortBy,
          sortOrder
        }
      });

    } catch (error) {
      console.error('Error fetching users:', error);
      res.status(500).json({ error: 'Failed to fetch users' });
    }
  }
);

// Create new user
app.post('/api/admin/users',
  authenticateAdmin,
  rateLimiters.adminAction,
  validateInput([
    validationRules.username,
    validationRules.email,
    validationRules.password,
    validationRules.role,
    validationRules.age,
    validationRules.description,
    validationRules.passions
  ]),
  auditLog('create_user'),
  async (req, res) => {
    try {
      const {
        username,
        email,
        password,
        role = 'user',
        age,
        description,
        passions = []
      } = req.body;

      // Check if username already exists
      const existingUser = await User.findOne({ username });
      if (existingUser) {
        return res.status(400).json({ error: 'Username already exists' });
      }

      // Check if email already exists (if provided)
      if (email) {
        const existingEmail = await User.findOne({ email });
        if (existingEmail) {
          return res.status(400).json({ error: 'Email already exists' });
        }
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10);

      // Create new user
      const newUser = new User({
        username,
        email: email || undefined,
        password: hashedPassword,
        role,
        age: age || undefined,
        description: description || '',
        passions: Array.isArray(passions) ? passions : [],
        accountStatus: 'active',
        createdAt: new Date(),
        updatedAt: new Date()
      });

      await newUser.save();

      // Return user without sensitive data
      const userResponse = newUser.toJSON();
      delete userResponse.password;

      console.log(`User created by admin ${req.user.username}: ${username}`);

      res.status(201).json({
        message: 'User created successfully',
        user: userResponse
      });

    } catch (error) {
      console.error('Error creating user:', error);
      res.status(500).json({ error: 'Failed to create user' });
    }
  }
);

// Update user
app.put('/api/admin/users/:userId',
  authenticateAdmin,
  rateLimiters.adminAction,
  validateInput([
    validationRules.username.optional(),
    validationRules.email,
    validationRules.role,
    validationRules.accountStatus,
    validationRules.age,
    validationRules.description,
    validationRules.passions
  ]),
  auditLog('update_user'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const updates = req.body;

      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: 'Invalid user ID format' });
      }

      // Find the user
      const user = await User.findById(userId);
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Check if username is being changed and if it's available
      if (updates.username && updates.username !== user.username) {
        const existingUser = await User.findOne({ username: updates.username });
        if (existingUser) {
          return res.status(400).json({ error: 'Username already exists' });
        }
      }

      // Check if email is being changed and if it's available
      if (updates.email && updates.email !== user.email) {
        const existingEmail = await User.findOne({ email: updates.email });
        if (existingEmail) {
          return res.status(400).json({ error: 'Email already exists' });
        }
      }

      // Prepare update object
      const updateObj = {};
      const allowedFields = ['username', 'email', 'role', 'accountStatus', 'age', 'description', 'passions'];

      allowedFields.forEach(field => {
        if (updates[field] !== undefined) {
          updateObj[field] = updates[field];
        }
      });

      updateObj.updatedAt = new Date();

      // Update user
      const updatedUser = await User.findByIdAndUpdate(
        userId,
        updateObj,
        { new: true, runValidators: true }
      ).select('-password -loginAttempts -lockUntil');

      console.log(`User updated by admin ${req.user.username}: ${updatedUser.username}`);

      res.status(200).json({
        message: 'User updated successfully',
        user: updatedUser
      });

    } catch (error) {
      console.error('Error updating user:', error);
      res.status(500).json({ error: 'Failed to update user' });
    }
  }
);

// Block/Unblock user
app.patch('/api/admin/users/:userId/status',
  authenticateAdmin,
  rateLimiters.adminAction,
  validateInput([
    validationRules.accountStatus
  ]),
  auditLog('change_user_status'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { accountStatus } = req.body;

      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: 'Invalid user ID format' });
      }

      const user = await User.findByIdAndUpdate(
        userId,
        {
          accountStatus,
          updatedAt: new Date()
        },
        { new: true, runValidators: true }
      ).select('-password -loginAttempts -lockUntil');

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Remove from active users if blocked
      if (accountStatus === 'blocked') {
        activeUsers.delete(userId);
      }

      console.log(`User status changed by admin ${req.user.username}: ${user.username} -> ${accountStatus}`);

      res.status(200).json({
        message: `User ${accountStatus} successfully`,
        user: user
      });

    } catch (error) {
      console.error('Error changing user status:', error);
      res.status(500).json({ error: 'Failed to change user status' });
    }
  }
);

// Reset user password
app.patch('/api/admin/users/:userId/password',
  authenticateAdmin,
  rateLimiters.sensitiveAction,
  validateInput([
    validationRules.password
  ]),
  auditLog('reset_user_password'),
  async (req, res) => {
    try {
      const { userId } = req.params;
      const { password } = req.body;

      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: 'Invalid user ID format' });
      }

      // Hash new password
      const hashedPassword = await bcrypt.hash(password, 10);

      const user = await User.findByIdAndUpdate(
        userId,
        {
          password: hashedPassword,
          loginAttempts: 0,
          lockUntil: undefined,
          updatedAt: new Date()
        },
        { new: true }
      ).select('username email role');

      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      console.log(`Password reset by admin ${req.user.username} for user: ${user.username}`);

      res.status(200).json({
        message: 'Password reset successfully',
        user: {
          id: user._id.toString(),
          username: user.username
        }
      });

    } catch (error) {
      console.error('Error resetting password:', error);
      res.status(500).json({ error: 'Failed to reset password' });
    }
  }
);

// Bulk operations on users
app.post('/api/admin/users/bulk',
  authenticateAdmin,
  rateLimiters.sensitiveAction,
  auditLog('bulk_user_operation'),
  async (req, res) => {
    try {
      const { action, userIds } = req.body;

      if (!action || !Array.isArray(userIds) || userIds.length === 0) {
        return res.status(400).json({ error: 'Action and userIds array are required' });
      }

      if (userIds.length > 50) {
        return res.status(400).json({ error: 'Maximum 50 users can be processed at once' });
      }

      // Validate all user IDs
      const invalidIds = userIds.filter(id => !mongoose.Types.ObjectId.isValid(id));
      if (invalidIds.length > 0) {
        return res.status(400).json({ error: 'Invalid user ID format', invalidIds });
      }

      let result = {};

      switch (action) {
        case 'block':
          result = await User.updateMany(
            { _id: { $in: userIds } },
            {
              accountStatus: 'blocked',
              updatedAt: new Date()
            }
          );

          // Remove blocked users from active users
          userIds.forEach(id => activeUsers.delete(id));
          break;

        case 'unblock':
          result = await User.updateMany(
            { _id: { $in: userIds } },
            {
              accountStatus: 'active',
              updatedAt: new Date()
            }
          );
          break;

        case 'delete':
          // Soft delete - mark as deleted
          result = await User.updateMany(
            { _id: { $in: userIds } },
            {
              accountStatus: 'deleted',
              updatedAt: new Date()
            }
          );

          // Remove deleted users from active users
          userIds.forEach(id => activeUsers.delete(id));
          break;

        case 'hard_delete':
          // Hard delete - actually remove from database
          // Also delete related data
          await Promise.all([
            Message.deleteMany({
              $or: [
                { senderId: { $in: userIds } },
                { receiverId: { $in: userIds } }
              ]
            }),
            Match.deleteMany({
              $or: [
                { user1: { $in: userIds } },
                { user2: { $in: userIds } }
              ]
            })
          ]);

          result = await User.deleteMany({ _id: { $in: userIds } });

          // Remove deleted users from active users
          userIds.forEach(id => activeUsers.delete(id));
          break;

        default:
          return res.status(400).json({ error: 'Invalid action. Supported: block, unblock, delete, hard_delete' });
      }

      console.log(`Bulk ${action} operation by admin ${req.user.username}: ${result.modifiedCount || result.deletedCount} users affected`);

      res.status(200).json({
        message: `Bulk ${action} operation completed successfully`,
        affected: result.modifiedCount || result.deletedCount,
        total: userIds.length
      });

    } catch (error) {
      console.error('Error in bulk operation:', error);
      res.status(500).json({ error: 'Bulk operation failed' });
    }
  }
);

// Get user activity/audit log
app.get('/api/admin/users/:userId/activity',
  authenticateAdmin,
  rateLimiters.adminAction,
  async (req, res) => {
    try {
      const { userId } = req.params;

      if (!mongoose.Types.ObjectId.isValid(userId)) {
        return res.status(400).json({ error: 'Invalid user ID format' });
      }

      const user = await User.findById(userId).select('-password -loginAttempts -lockUntil');
      if (!user) {
        return res.status(404).json({ error: 'User not found' });
      }

      // Get user's matches and messages count
      const [matchCount, messageCount, sentMessages, receivedMessages] = await Promise.all([
        Match.countDocuments({
          $or: [{ user1: userId }, { user2: userId }]
        }),
        Message.countDocuments({
          $or: [{ senderId: userId }, { receiverId: userId }]
        }),
        Message.countDocuments({ senderId: userId }),
        Message.countDocuments({ receiverId: userId })
      ]);

      // Get recent matches
      const recentMatches = await Match.find({
        $or: [{ user1: userId }, { user2: userId }]
      })
      .populate('user1', 'username')
      .populate('user2', 'username')
      .sort({ createdAt: -1 })
      .limit(10);

      const activity = {
        user: user,
        stats: {
          totalMatches: matchCount,
          totalMessages: messageCount,
          sentMessages: sentMessages,
          receivedMessages: receivedMessages,
          isOnline: user.isOnline,
          accountAge: Math.floor((new Date() - new Date(user.createdAt)) / (1000 * 60 * 60 * 24)) // days
        },
        recentMatches: recentMatches.map(match => ({
          id: match._id.toString(),
          partner: match.user1._id.toString() === userId ? match.user2.username : match.user1.username,
          createdAt: match.createdAt
        }))
      };

      res.status(200).json(activity);

    } catch (error) {
      console.error('Error fetching user activity:', error);
      res.status(500).json({ error: 'Failed to fetch user activity' });
    }
  }
);

// ===== Socket.IO Event Handlers =====

io.on('connection', (socket) => {
  console.log('New client connected');
  let currentUserId = null;

  // ENHANCED: User registration with socket
  socket.on('register', ({ userId, username }) => {
    console.log(`User ${username} (${userId}) attempting to register with socket`);

    if (!userId) {
      console.error('Registration failed: Missing userId');
      return;
    }

    if (!username) {
      console.error('Registration failed: Missing username');
      return;
    }

    currentUserId = userId;

    // Check if user is already registered
    const existingUser = activeUsers.get(userId);
    if (existingUser) {
      console.log(`Updating existing user ${username} (${userId})`);
      existingUser.socket = socket;
      existingUser.username = username;
      activeUsers.set(userId, existingUser);
    } else {
      console.log(`Registering new user ${username} (${userId})`);
      activeUsers.set(userId, {
        socket,
        username,
        location: null,
        maxDistance: 5, // Default 5km
        lastShake: null
      });
    }

    console.log(`User ${username} (${userId}) registered with socket`);
    console.log(`Total active users: ${activeUsers.size}`);

    // Debug: log all active users
    logActiveUsers();
  });

  // Update user location
  socket.on('updateLocation', ({ userId, location }) => {
    console.log(`Location update for user ${userId}:`, JSON.stringify(location));

    if (!userId) {
      console.error('Invalid updateLocation: Missing userId');
      return;
    }

    if (!location || !location.latitude || !location.longitude) {
      console.error('Invalid updateLocation: Invalid location data');
      return;
    }

    const user = activeUsers.get(userId);
    if (user) {
      user.location = location;
      activeUsers.set(userId, user);
      console.log(`Location updated for user ${userId}`);
    } else {
      console.error(`Cannot update location: User ${userId} not found in active users`);
    }
  });

  // Handle explicit allow rematch requests
  socket.on('allowRematch', ({ userId, otherUserId }) => {
    if (!userId || !otherUserId) return;

    // Create unique identifier for this pair of users (order doesn't matter)
    const pairKey = [userId, otherUserId].sort().join('_');

    // Remove any restrictions for this pair
    matchRestrictions.delete(pairKey);

    console.log(`Rematch allowed between ${userId} and ${otherUserId}`);
  });

  // Handle chat deleted events
  socket.on('chatDeleted', ({ userId, otherUserId, reason, allowRematch }) => {
    if (!userId || !otherUserId) return;

    // Create unique identifier for this pair of users (order doesn't matter)
    const pairKey = [userId, otherUserId].sort().join('_');

    if (allowRematch) {
      // If rematch is allowed, remove any restrictions
      matchRestrictions.delete(pairKey);
      console.log(`Chat deleted with rematch allowed between ${userId} and ${otherUserId}`);
    } else {
      // If rematch is not allowed (likely due to blocking), set restriction
      matchRestrictions.set(pairKey, true);
      console.log(`Chat deleted with rematch restricted between ${userId} and ${otherUserId}`);
    }
  });

  // Handle reset match restrictions - use this when user logs in
  socket.on('resetMatchRestrictions', ({ userId }) => {
    if (!userId) return;

    // Find and remove all restrictions involving this user
    for (const [pairKey, _] of matchRestrictions.entries()) {
      const userIds = pairKey.split('_');
      if (userIds.includes(userId)) {
        matchRestrictions.delete(pairKey);
        console.log(`Removed match restriction for pair: ${pairKey}`);
      }
    }

    console.log(`Reset match restrictions for user ${userId}`);
  });

  // Sync blocked users
  socket.on('syncBlocked', ({ userId, blockedUsers }) => {
    if (userId && blockedUsers && Array.isArray(blockedUsers)) {
      const user = activeUsers.get(userId);
      if (user) {
        user.blockedUsers = blockedUsers;
        activeUsers.set(userId, user);
        console.log(`Synced blocked users for ${userId}: ${blockedUsers.length} blocked`);
      }
    }
  });


  // Handle user blocking another user
  socket.on('blockUser', ({ userId, blockedUserId }) => {
    console.log(`User ${userId} blocked user ${blockedUserId}`);

    // Find the blocked user's socket
    const blockedUser = activeUsers.get(blockedUserId);

    // If the blocked user is online, notify them immediately
    if (blockedUser && blockedUser.socket) {
      blockedUser.socket.emit('userBlockedYou', {
        userId,
        blockedUserId
      });
      console.log(`Notified user ${blockedUserId} that they were blocked`);
    }
  });

  // handler to check block status
  socket.on('checkBlockStatus', async ({ userId, otherUserId }) => {
    if (!userId || !otherUserId) return;

    try {
      // Check if the other user has blocked this user
      const pairKey = [userId, otherUserId].sort().join('_');
      const isBlocked = matchRestrictions.has(pairKey);

      // Also check if the other user has the user in their blockedUsers array
      const otherUser = activeUsers.get(otherUserId);
      const isBlockedByArray = otherUser &&
                               otherUser.blockedUsers &&
                               otherUser.blockedUsers.includes(userId);

      // Send back the block status
      socket.emit('blockStatus', {
        isBlocked: isBlocked || isBlockedByArray
      });
    } catch (error) {
      console.error('Error checking block status:', error);
      socket.emit('blockStatus', { isBlocked: false });
    }
  });

  // Handle unblock user
  socket.on('unblockUser', ({ userId, unblockedUserId }) => {
    if (!userId || !unblockedUserId) return;

    // Create unique identifier for this pair of users (order doesn't matter)
    const pairKey = [userId, unblockedUserId].sort().join('_');

    // Remove match restriction
    matchRestrictions.delete(pairKey);
    console.log(`User ${userId} unblocked ${unblockedUserId}, match restriction removed`);
  });

  // ENHANCED: Handle shake event with better logging and validation
  socket.on('shake', async ({ userId, username, location, maxDistance, timestamp, blockedUsers = [], rematchEnabled = true, minAge = 18, maxAge = 100 }) => {
    console.log(`Shake from user ${username} (${userId})`);
    console.log(`User location:`, JSON.stringify(location));
    console.log(`User maxDistance: ${maxDistance}km`);

    if (!userId || !location) {
      console.error('Invalid shake data: Missing userId or location');
      return;
    }

    // Update user's last shake and max distance
    const user = activeUsers.get(userId);
    if (user) {
      user.lastShake = timestamp;
      user.maxDistance = maxDistance;
      user.location = location;
      activeUsers.set(userId, user);

      console.log(`Updated shake info for user ${username} (${userId})`);
      console.log(`Active users: ${activeUsers.size}`);

      // Log all active users for debugging
      logActiveUsers();

      // Check for matches with other users who shook in the last 5 seconds
      const now = new Date();
      const fiveSecondsAgo = new Date(now - 5000); // 5 seconds ago

      // Find potential matches
      for (const [otherUserId, otherUser] of activeUsers.entries()) {
        // Skip the current user
        if (otherUserId === userId) {
          console.log(`Skipping self match: ${userId}`);
          continue;
        }

        // Skip users who haven't shaken recently
        if (!otherUser.lastShake) {
          console.log(`Skipping user ${otherUserId}: No shake recorded`);
          continue;
        }

        const otherShakeTime = new Date(otherUser.lastShake);
        if (otherShakeTime < fiveSecondsAgo) {
          console.log(`Skipping user ${otherUserId}: Shake too old (${otherShakeTime.toISOString()})`);
          continue;
        }

        // Skip users without location
        if (!otherUser.location || !user.location) {
          console.log(`Skipping user ${otherUserId}: Missing location data`);
          continue;
        }

        // Skip blocked users
        if (blockedUsers && blockedUsers.includes(otherUserId)) {
          console.log(`Skipping user ${otherUserId}: User is blocked`);
          continue;
        }

        // Check for match restrictions
        const pairKey = [userId, otherUserId].sort().join('_');
        if (!rematchEnabled || matchRestrictions.has(pairKey)) {
          console.log(`Match restricted between ${userId} and ${otherUserId}`);
          continue; // Skip this potential match
        }

        // Calculate distance between users
        try {
          const distanceInMeters = geolib.getDistance(
            { latitude: user.location.latitude, longitude: user.location.longitude },
            { latitude: otherUser.location.latitude, longitude: otherUser.location.longitude }
          );

          const distanceInKm = distanceInMeters / 1000;
          console.log(`Distance between ${userId} and ${otherUserId}: ${distanceInKm.toFixed(2)} km`);

          // Check if users are within each other's max distance
          if (distanceInKm <= user.maxDistance && distanceInKm <= otherUser.maxDistance) {
            console.log(`Match found between ${username} and ${otherUser.username}!`);

            try {
              // Ensure both userIds are valid MongoDB ObjectIds
              if (!mongoose.Types.ObjectId.isValid(userId) || !mongoose.Types.ObjectId.isValid(otherUserId)) {
                console.error(`Invalid user ID format. userId: ${userId}, otherUserId: ${otherUserId}`);
                continue;
              }

              // Create new match in database - ensuring all required fields are provided
              const newMatch = new Match({
                user1: userId,
                user2: otherUserId,
                location1: user.location,
                location2: otherUser.location,
                distance: Math.round(distanceInKm * 10) / 10, // Round to 1 decimal place
              });

              // Debug log to verify all required fields are present
              console.log(`Creating match with: user1=${userId}, user2=${otherUserId}, distance=${newMatch.distance}`);

              await newMatch.save();
              console.log(`Match saved to database with ID: ${newMatch._id}`);

              // Notify both users of the match
              const matchData1 = {
                matchId: newMatch._id.toString(), // Convert ObjectId to string
                userId: otherUserId,
                username: otherUser.username,
                distance: newMatch.distance,
                timestamp: newMatch.createdAt
              };

              const matchData2 = {
                matchId: newMatch._id.toString(), // Convert ObjectId to string
                userId: userId,
                username: username,
                distance: newMatch.distance,
                timestamp: newMatch.createdAt
              };

              // Log what we're sending to make debugging easier
              console.log(`Sending match notification to ${username} about ${otherUser.username}`);
              console.log(`Match data being sent:`, JSON.stringify(matchData1));

              console.log(`Sending match notification to ${otherUser.username} about ${username}`);
              console.log(`Match data being sent:`, JSON.stringify(matchData2));

              if (!user.socket) {
                console.error(`Cannot send match notification: User ${userId} socket is null`);
              } else {
                user.socket.emit('match', matchData1);
                // Wait a small interval before navigation to ensure match data is received
                setTimeout(() => {
                  user.socket.emit('navigate', { screen: 'Match', params: { match: matchData1 } });
                }, 300);
              }

              if (!otherUser.socket) {
                console.error(`Cannot send match notification: User ${otherUserId} socket is null`);
              } else {
                otherUser.socket.emit('match', matchData2);
                // Wait a small interval before navigation to ensure match data is received
                setTimeout(() => {
                  otherUser.socket.emit('navigate', { screen: 'Match', params: { match: matchData2 } });
                }, 300);
              }
            } catch (error) {
              console.error('Error creating match:', error);
            }
          } else {
            console.log(`Users not within each other's max distance. User1: ${user.maxDistance}km, User2: ${otherUser.maxDistance}km`);
          }
        } catch (distanceError) {
          console.error('Error calculating distance:', distanceError);
        }
      }
    } else {
      console.error(`User ${userId} not found in active users`);
      // Try to add the user since they're clearly online
      activeUsers.set(userId, {
        socket,
        username,
        location,
        maxDistance,
        lastShake: timestamp
      });
      console.log(`Added missing user ${username} (${userId}) to active users`);
    }
  });

  // Handle mark messages as read
  socket.on('markMessagesAsRead', async ({ userId, otherUserId }) => {
    try {
      // Update read status in database
      await Message.updateMany(
        { senderId: otherUserId, receiverId: userId, read: false },
        { read: true }
      );

      // Notify sender that messages were read
      const sender = activeUsers.get(otherUserId);
      if (sender && sender.socket) {
        sender.socket.emit('messagesRead', { userId: otherUserId, otherUserId: userId });
      }

      console.log(`Messages from ${otherUserId} to ${userId} marked as read`);
    } catch (error) {
      console.error('Error marking messages as read:', error);
    }
  });

  // Handle message read
  socket.on('messageRead', async ({ messageId, userId, otherUserId }) => {
    try {
      // Update message read status
      await Message.findOneAndUpdate(
        { messageId },
        { read: true }
      );

      console.log(`Message ${messageId} marked as read`);
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  });

  // Handle get unread messages
  socket.on('getUnreadMessages', async ({ userId, otherUserId }) => {
    try {
      // Find all unread messages between these users
      const unreadMessages = await Message.find({
        $or: [
          { senderId: userId, receiverId: otherUserId },
          { senderId: otherUserId, receiverId: userId }
        ]
      }).sort({ timestamp: 1 });

      // Format messages for client
      const formattedMessages = unreadMessages.map(msg => ({
        id: msg.messageId,
        text: msg.text,
        senderId: msg.senderId,
        timestamp: msg.timestamp,
      }));

      // Send messages to client
      socket.emit('unreadMessages', formattedMessages);

      console.log(`Sent ${formattedMessages.length} messages to ${userId}`);
    } catch (error) {
      console.error('Error getting unread messages:', error);
    }
  });

  // Handle sending messages
  socket.on('sendMessage', async (messageData) => {
    try {
      const { id, senderId, senderUsername, receiverId, receiverUsername, text, timestamp } = messageData;

      // Store message in database
      const newMessage = new Message({
        messageId: id,
        senderId,
        senderUsername,
        receiverId,
        receiverUsername,
        text,
        timestamp
      });

      await newMessage.save();

      // Find receiver's socket and forward the message
      const receiver = activeUsers.get(receiverId);
      if (receiver && receiver.socket) {
        receiver.socket.emit('message', messageData);
      }

      console.log(`Message sent from ${senderUsername} to ${receiverUsername}`);
    } catch (error) {
      console.error('Error sending message:', error);
    }
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    if (currentUserId) {
      // Don't remove from active users, just note they're disconnected
      const user = activeUsers.get(currentUserId);
      if (user) {
        console.log(`User ${currentUserId} disconnected, marking socket as null`);
        user.socket = null;
        activeUsers.set(currentUserId, user);
      } else {
        activeUsers.delete(currentUserId);
        console.log(`User ${currentUserId} removed from active users`);
      }
    }
  });
});

// Start server - Accept connections from all interfaces
const PORT = process.env.PORT || 3000;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`Server running on port ${PORT}`);
  console.log(`Desktop app: http://localhost:${PORT}/`);
  console.log(`Admin panel: http://localhost:${PORT}/admin`);
  console.log(`External access: http://**************:${PORT}/`);
});